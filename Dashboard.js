import React, { useState, useEffect } from 'react';

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    todayStats: {
      samplesAnalyzed: 0,
      castsCompleted: 0,
      revenue: 0,
      pendingOperations: 0
    },
    recentActivities: [],
    pendingInvoices: [],
    topCustomers: []
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // محاكاة تحميل البيانات
    const loadDashboardData = async () => {
      setIsLoading(true);
      
      // محاكاة استدعاء API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // بيانات تجريبية
      setDashboardData({
        todayStats: {
          samplesAnalyzed: 15,
          castsCompleted: 8,
          revenue: 12500,
          pendingOperations: 3
        },
        recentActivities: [
          {
            id: 1,
            type: 'sample',
            description: 'تم تحليل عينة ذهب خام للعميل أحمد محمد',
            time: '10:30 ص',
            status: 'completed'
          },
          {
            id: 2,
            type: 'casting',
            description: 'تم صب سبيكة 50 غرام للعميل فاطمة علي',
            time: '09:15 ص',
            status: 'completed'
          },
          {
            id: 3,
            type: 'invoice',
            description: 'تم إنشاء فاتورة جديدة للعميل محمد أحمد',
            time: '08:45 ص',
            status: 'pending'
          }
        ],
        pendingInvoices: [
          {
            id: 1,
            customerName: 'أحمد محمد',
            amount: 750,
            dueDate: '2025-07-05'
          },
          {
            id: 2,
            customerName: 'سارة أحمد',
            amount: 1200,
            dueDate: '2025-07-03'
          }
        ],
        topCustomers: [
          {
            id: 1,
            name: 'محمد علي',
            totalOperations: 25,
            totalRevenue: 15000
          },
          {
            id: 2,
            name: 'فاطمة أحمد',
            totalOperations: 18,
            totalRevenue: 12000
          }
        ]
      });
      
      setIsLoading(false);
    };

    loadDashboardData();
  }, []);

  const getActivityIcon = (type) => {
    switch (type) {
      case 'sample':
        return '🔬';
      case 'casting':
        return '🏭';
      case 'invoice':
        return '🧾';
      default:
        return '📋';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return '#28a745';
      case 'pending':
        return '#ffc107';
      case 'overdue':
        return '#dc3545';
      default:
        return '#6c757d';
    }
  };

  if (isLoading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="dashboard">
      <div className="card">
        <div className="card-header">
          <h1 className="card-title">لوحة التحكم الرئيسية</h1>
          <div style={{ fontSize: '14px', color: '#666' }}>
            آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}
          </div>
        </div>

        {/* إحصائيات اليوم */}
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-value">{dashboardData.todayStats.samplesAnalyzed}</div>
            <div className="stat-label">عينات محللة اليوم</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-value">{dashboardData.todayStats.castsCompleted}</div>
            <div className="stat-label">سبائك مصبوبة اليوم</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-value">{dashboardData.todayStats.revenue.toLocaleString()} ر.س</div>
            <div className="stat-label">إيرادات اليوم</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-value">{dashboardData.todayStats.pendingOperations}</div>
            <div className="stat-label">عمليات معلقة</div>
          </div>
        </div>

        {/* الأنشطة الحديثة والفواتير المعلقة */}
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginTop: '30px' }}>
          {/* الأنشطة الحديثة */}
          <div className="card">
            <div className="card-header">
              <h3>الأنشطة الحديثة</h3>
            </div>
            <div className="activities-list">
              {dashboardData.recentActivities.map((activity) => (
                <div key={activity.id} className="activity-item" style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '15px',
                  borderBottom: '1px solid #e0e0e0',
                  gap: '15px'
                }}>
                  <div style={{ fontSize: '24px' }}>
                    {getActivityIcon(activity.type)}
                  </div>
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>
                      {activity.description}
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      {activity.time}
                    </div>
                  </div>
                  <div style={{
                    width: '10px',
                    height: '10px',
                    borderRadius: '50%',
                    backgroundColor: getStatusColor(activity.status)
                  }}></div>
                </div>
              ))}
            </div>
          </div>

          {/* الفواتير المعلقة */}
          <div className="card">
            <div className="card-header">
              <h3>الفواتير المعلقة</h3>
            </div>
            <div className="invoices-list">
              {dashboardData.pendingInvoices.map((invoice) => (
                <div key={invoice.id} className="invoice-item" style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '15px',
                  borderBottom: '1px solid #e0e0e0'
                }}>
                  <div>
                    <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>
                      {invoice.customerName}
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      تاريخ الاستحقاق: {invoice.dueDate}
                    </div>
                  </div>
                  <div style={{ fontWeight: 'bold', color: '#dc3545' }}>
                    {invoice.amount.toLocaleString()} ر.س
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* أهم العملاء */}
        <div className="card" style={{ marginTop: '20px' }}>
          <div className="card-header">
            <h3>أهم العملاء</h3>
          </div>
          <div className="table-container">
            <table className="table">
              <thead>
                <tr>
                  <th>اسم العميل</th>
                  <th>عدد العمليات</th>
                  <th>إجمالي الإيرادات</th>
                </tr>
              </thead>
              <tbody>
                {dashboardData.topCustomers.map((customer) => (
                  <tr key={customer.id}>
                    <td>{customer.name}</td>
                    <td>{customer.totalOperations}</td>
                    <td>{customer.totalRevenue.toLocaleString()} ر.س</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

