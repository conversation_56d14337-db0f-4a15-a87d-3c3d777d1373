@echo off
echo.
echo ========================================
echo Gold Lab Management System
echo ========================================
echo.

echo Checking system requirements...

:: Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not installed. Please install Python 3.11 or newer
    pause
    exit /b 1
)
echo OK: Python available

:: Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Node.js not installed. Running console version only
    goto :run_python_only
)
echo OK: Node.js available

:: Check npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: npm not available. Running console version only
    goto :run_python_only
)
echo OK: npm available

echo.
echo Choose running mode:
echo 1. Console version (Python)
echo 2. Web interface (React)
echo 3. Both
echo.
set /p choice="Choose number (1-3): "

if "%choice%"=="1" goto :run_python_only
if "%choice%"=="2" goto :run_react_only
if "%choice%"=="3" goto :run_both
goto :invalid_choice

:run_python_only
echo.
echo Running console version...
echo ========================================
python main.py
goto :end

:run_react_only
echo.
echo Running web interface...
echo ========================================
echo Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)
echo OK: Dependencies installed successfully
echo.
echo Starting server...
echo Browser will open automatically at http://localhost:3000
call npm start
goto :end

:run_both
echo.
echo Running integrated system...
echo ========================================
echo Installing React dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)
echo OK: Dependencies installed successfully
echo.
echo Starting server...
echo Browser will open automatically at http://localhost:3000
echo.
echo Note: You can also run the console version in a separate window with:
echo python main.py
echo.
call npm start
goto :end

:invalid_choice
echo ERROR: Invalid choice
pause
exit /b 1

:end
echo.
echo Thank you for using Gold Lab Management System
pause
