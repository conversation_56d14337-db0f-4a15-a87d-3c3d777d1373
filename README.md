# نظام إدارة معمل الذهب - Gold Lab Management System

## 🏆 نظرة عامة

نظام إدارة معمل الذهب هو تطبيق شامل لإدارة جميع عمليات معامل الذهب والمعادن النفيسة. يوفر النظام ثلاث واجهات للاستخدام:

1. **تطبيق سطح المكتب (Desktop App)**: واجهة رسومية متقدمة باستخدام Tkinter ⭐ **الأفضل**
2. **واجهة نصية (Console)**: للاستخدام السريع والمباشر
3. **واجهة رسومية (Web App)**: واجهة حديثة باستخدام React

## 🚀 طرق التشغيل

### 🖥️ تطبيق سطح المكتب (الموصى به)
```bash
# الطريقة السريعة
run_desktop.bat

# أو باستخدام Python مباشرة
python run_desktop.py

# أو تشغيل التطبيق مباشرة
python desktop_app.py
```

### 📱 النظام المتكامل (جميع الواجهات)
```bash
# تشغيل ملف التشغيل التلقائي
run_system.bat
```

### 💻 النسخة النصية فقط
```bash
python main.py
```

### 🌐 الواجهة الرسومية فقط
```bash
# تثبيت التبعيات (مرة واحدة فقط)
npm install

# تشغيل الخادم
npm start
```

## 📋 بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

⚠️ **تحذير**: يجب تغيير كلمة المرور الافتراضية فور تسجيل الدخول لأول مرة.

## 🔧 متطلبات النظام

### متطلبات أساسية
- **Python 3.8** أو أحدث (يُنصح بـ Python 3.11)
- **SQLite** (مدمج مع Python)
- **Tkinter** (مدمج مع Python في معظم التوزيعات)

### متطلبات تطبيق سطح المكتب
- **Python 3.8+** مع Tkinter
- جميع المكتبات مدمجة مع Python (لا حاجة لتثبيت إضافي)

### متطلبات الواجهة الرسومية (اختيارية)
- **Node.js 18.0.0** أو أحدث
- **npm 8.0.0** أو أحدث

## 📁 هيكل المشروع

```
PP2026/
├── 🖥️ Desktop Application
│   ├── desktop_app.py          # تطبيق سطح المكتب الرئيسي
│   ├── customer_dialog.py      # حوارات إدارة العملاء والموظفين
│   ├── run_desktop.py          # مشغل تطبيق سطح المكتب
│   └── run_desktop.bat         # ملف تشغيل سطح المكتب
│
├── 🐍 Python Backend
│   ├── main.py                 # التطبيق الرئيسي النصي
│   ├── main_controller.py      # المتحكم الرئيسي
│   ├── database_manager.py     # مدير قاعدة البيانات
│   ├── customer.py            # نموذج العميل
│   ├── employee.py            # نموذج الموظف
│   ├── operations.py          # نموذج العمليات
│   ├── invoice.py             # نموذج الفواتير
│   └── gold_lab.db           # قاعدة البيانات
│
├── ⚛️ React Frontend
│   ├── src/
│   │   ├── App.js            # التطبيق الرئيسي
│   │   ├── App.css           # ملف الأنماط
│   │   ├── index.js          # نقطة الدخول
│   │   └── components/       # مكونات React
│   ├── public/
│   │   ├── index.html        # الصفحة الرئيسية
│   │   └── manifest.json     # إعدادات التطبيق
│   └── package.json          # تبعيات React
│
├── 📄 Documentation
│   ├── README.md             # هذا الملف
│   ├── دليل المستخدم الشامل.md
│   └── ملخص مشروع نظام إدارة معمل الذهب.md
│
└── 🚀 Scripts
    └── run_system.bat        # ملف التشغيل التلقائي
```

## 🎯 الميزات الرئيسية

### 🖥️ تطبيق سطح المكتب المتقدم ⭐
- **واجهة عربية متكاملة**: دعم كامل للغة العربية مع تخطيط RTL
- **تصميم حديث**: واجهة مستخدم عصرية وسهلة الاستخدام
- **لوحة تحكم تفاعلية**: عرض الإحصائيات والمقاييس الرئيسية
- **نظام تسجيل دخول آمن**: حماية بكلمة مرور مشفرة
- **قوائم سياق**: نقر يمين للوصول السريع للعمليات
- **جداول تفاعلية**: فرز وتصفية وبحث متقدم

### 👥 إدارة العملاء
- إضافة وتعديل وحذف العملاء بواجهة سهلة
- البحث المتقدم في قاعدة بيانات العملاء
- جداول تفاعلية مع إمكانية الفرز والتصفية
- تتبع تاريخ العملاء والعمليات
- حوارات منبثقة لإدخال البيانات

### 👨‍💼 إدارة الموظفين
- إدارة بيانات الموظفين مع المناصب المختلفة
- تحديد الأدوار والصلاحيات (محلل، فني تنقية، فني صب، مشرف، مدير)
- واجهة سهلة لإضافة وتعديل الموظفين
- تتبع أداء الموظفين
- قوائم منسدلة للمناصب المحددة مسبقاً

### 🔬 إدارة العمليات
- **تحليل العينات**: إضافة عينات وتسجيل نتائج التحليل
- **التنقية**: إدارة عمليات تنقية المعادن
- **الصب**: تسجيل عمليات صب السبائك
- تتبع حالة العمليات (معلقة/مكتملة)

### 💰 إدارة الفواتير
- إنشاء فواتير للعمليات المختلفة
- تتبع المدفوعات والمبالغ المتبقية
- تقارير مالية مفصلة

### 📊 التقارير والإحصائيات
- لوحة تحكم مع مقاييس الأداء الرئيسية
- تقارير يومية وأسبوعية وشهرية
- إحصائيات العمليات والأداء في الوقت الفعلي
- تقارير مالية شاملة
- مؤشرات الأداء الرئيسية

## 🔒 الأمان

- تشفير كلمات المرور باستخدام SHA-256
- نظام تسجيل دخول آمن
- تسجيل جميع العمليات للمراجعة
- نسخ احتياطية تلقائية

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **خطأ "ModuleNotFoundError"**
   ```bash
   # تأكد من وجود جميع الملفات في نفس المجلد
   # تأكد من تشغيل الأمر من المجلد الصحيح
   ```

2. **خطأ في قاعدة البيانات**
   ```bash
   # احذف ملف gold_lab.db وأعد تشغيل البرنامج
   # سيتم إنشاء قاعدة بيانات جديدة تلقائياً
   ```

3. **مشاكل في React**
   ```bash
   # احذف مجلد node_modules وأعد تثبيت التبعيات
   rm -rf node_modules
   npm install
   ```

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع دليل المستخدم الشامل
- تحقق من ملفات التوثيق المرفقة
- تأكد من تحديث النظام لآخر إصدار

## 📝 ملاحظات مهمة

1. **النسخ الاحتياطية**: يُنصح بإنشاء نسخ احتياطية دورية من قاعدة البيانات
2. **الأمان**: غيّر كلمة المرور الافتراضية فور التثبيت
3. **التحديثات**: تحقق من وجود تحديثات دورياً
4. **الأداء**: لأفضل أداء، استخدم متصفح Chrome أو Firefox الحديث

---

**تم تطوير هذا النظام خصيصاً لمعامل الذهب والمعادن النفيسة** 🏆
