#!/usr/bin/env python3
"""
ملف تشغيل تطبيق سطح المكتب
Desktop Application Launcher
"""

import sys
import os
import subprocess
from pathlib import Path

def check_requirements():
    """التحقق من متطلبات النظام"""
    print("🔍 التحقق من متطلبات النظام...")
    
    # التحقق من Python
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # التحقق من tkinter
    try:
        import tkinter
        print("✅ Tkinter متوفر")
    except ImportError:
        print("❌ Tkinter غير متوفر. يرجى تثبيت python3-tk")
        return False
    
    # التحقق من الملفات المطلوبة
    required_files = [
        'desktop_app.py',
        'customer_dialog.py',
        'main_controller.py',
        'database_manager.py',
        'customer.py',
        'employee.py',
        'operations.py',
        'invoice.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ جميع الملفات المطلوبة متوفرة")
    return True

def install_requirements():
    """تثبيت المتطلبات إذا لزم الأمر"""
    print("\n📦 التحقق من المكتبات المطلوبة...")
    
    # قائمة المكتبات المطلوبة (كلها مدمجة مع Python)
    required_modules = [
        'tkinter',
        'sqlite3',
        'datetime',
        'hashlib',
        'os',
        'sys'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ مكتبات مفقودة:")
        for module in missing_modules:
            print(f"   - {module}")
        return False
    
    print("✅ جميع المكتبات متوفرة")
    return True

def create_shortcut():
    """إنشاء اختصار على سطح المكتب (Windows)"""
    if sys.platform != 'win32':
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "نظام إدارة معمل الذهب.lnk")
        target = os.path.join(os.getcwd(), "run_desktop.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✅ تم إنشاء اختصار على سطح المكتب")
    except ImportError:
        print("⚠️  لإنشاء اختصار، قم بتثبيت: pip install pywin32 winshell")
    except Exception as e:
        print(f"⚠️  فشل في إنشاء الاختصار: {e}")

def run_desktop_app():
    """تشغيل تطبيق سطح المكتب"""
    print("\n🚀 بدء تشغيل تطبيق سطح المكتب...")
    
    try:
        # استيراد وتشغيل التطبيق
        from desktop_app import GoldLabDesktopApp
        
        print("✅ تم تحميل التطبيق بنجاح")
        print("📱 فتح نافذة التطبيق...")
        
        app = GoldLabDesktopApp()
        app.run()
        
    except ImportError as e:
        print(f"❌ فشل في استيراد التطبيق: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("🏆 نظام إدارة معمل الذهب - تطبيق سطح المكتب")
    print("Gold Lab Management System - Desktop Application")
    print("="*60)
    
    # التحقق من المتطلبات
    if not check_requirements():
        print("\n❌ فشل في التحقق من المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من المكتبات
    if not install_requirements():
        print("\n❌ فشل في التحقق من المكتبات")
        input("اضغط Enter للخروج...")
        return
    
    print("\n✅ جميع المتطلبات متوفرة")
    
    # عرض خيارات التشغيل
    print("\n" + "="*40)
    print("خيارات التشغيل:")
    print("1. تشغيل التطبيق")
    print("2. إنشاء اختصار على سطح المكتب")
    print("3. تشغيل التطبيق وإنشاء اختصار")
    print("4. خروج")
    print("="*40)
    
    while True:
        try:
            choice = input("\nاختر الرقم (1-4): ").strip()
            
            if choice == "1":
                if run_desktop_app():
                    print("\n✅ تم إغلاق التطبيق بنجاح")
                break
                
            elif choice == "2":
                create_shortcut()
                break
                
            elif choice == "3":
                create_shortcut()
                if run_desktop_app():
                    print("\n✅ تم إغلاق التطبيق بنجاح")
                break
                
            elif choice == "4":
                print("👋 شكراً لاستخدام نظام إدارة معمل الذهب")
                break
                
            else:
                print("❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى")
                
        except KeyboardInterrupt:
            print("\n\n👋 تم إلغاء العملية")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
