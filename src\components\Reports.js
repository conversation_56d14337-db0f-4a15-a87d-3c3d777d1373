import React, { useState, useEffect } from 'react';

const Reports = () => {
  const [activeReportType, setActiveReportType] = useState('daily');
  const [reportData, setReportData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [dateRange, setDateRange] = useState({
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [selectedMonth, setSelectedMonth] = useState({
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1
  });

  const reportTypes = [
    { id: 'daily', label: 'تقرير يومي', icon: '📅' },
    { id: 'weekly', label: 'تقرير أسبوعي', icon: '📊' },
    { id: 'monthly', label: 'تقرير شهري', icon: '📈' },
    { id: 'custom', label: 'تقرير مخصص', icon: '🔍' }
  ];

  useEffect(() => {
    generateReport();
  }, [activeReportType]);

  const generateReport = async () => {
    setIsLoading(true);
    
    // محاكاة استدعاء API
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // بيانات تجريبية للتقارير
    const mockReportData = {
      daily: {
        period: 'اليوم - ' + new Date().toLocaleDateString('ar-SA'),
        operations: {
          samplesAnalyzed: 15,
          refinementsPerformed: 8,
          castingsPerformed: 5,
          totalCastedWeight: 125.5
        },
        financial: {
          totalInvoices: 12,
          totalAmount: 18500,
          totalPaid: 15200,
          totalOutstanding: 3300,
          paidInvoices: 9,
          unpaidInvoices: 3
        },
        employeePerformance: [
          { name: 'سارة أحمد المحمد', role: 'محلل', operations: 8, rating: 4.8 },
          { name: 'محمد علي السالم', role: 'فني تنقية', operations: 5, rating: 4.6 },
          { name: 'فاطمة عبدالله الأحمد', role: 'فني صب', operations: 3, rating: 4.7 }
        ],
        topCustomers: [
          { name: 'أحمد محمد علي', operations: 5, revenue: 3750 },
          { name: 'فاطمة أحمد السالم', operations: 3, revenue: 2200 },
          { name: 'محمد عبدالله الأحمد', operations: 4, revenue: 2950 }
        ]
      },
      weekly: {
        period: 'الأسبوع الحالي',
        operations: {
          samplesAnalyzed: 85,
          refinementsPerformed: 42,
          castingsPerformed: 28,
          totalCastedWeight: 750.2
        },
        financial: {
          totalInvoices: 65,
          totalAmount: 95000,
          totalPaid: 78500,
          totalOutstanding: 16500,
          paidInvoices: 52,
          unpaidInvoices: 13
        },
        employeePerformance: [
          { name: 'سارة أحمد المحمد', role: 'محلل', operations: 35, rating: 4.8 },
          { name: 'محمد علي السالم', role: 'فني تنقية', operations: 25, rating: 4.6 },
          { name: 'فاطمة عبدالله الأحمد', role: 'فني صب', operations: 18, rating: 4.7 }
        ],
        topCustomers: [
          { name: 'أحمد محمد علي', operations: 15, revenue: 18750 },
          { name: 'فاطمة أحمد السالم', operations: 12, revenue: 14200 },
          { name: 'محمد عبدالله الأحمد', operations: 18, revenue: 22950 }
        ]
      },
      monthly: {
        period: `شهر ${getMonthName(selectedMonth.month)} ${selectedMonth.year}`,
        operations: {
          samplesAnalyzed: 320,
          refinementsPerformed: 185,
          castingsPerformed: 125,
          totalCastedWeight: 3250.8
        },
        financial: {
          totalInvoices: 285,
          totalAmount: 425000,
          totalPaid: 380000,
          totalOutstanding: 45000,
          paidInvoices: 245,
          unpaidInvoices: 40
        },
        employeePerformance: [
          { name: 'سارة أحمد المحمد', role: 'محلل', operations: 145, rating: 4.8 },
          { name: 'محمد علي السالم', role: 'فني تنقية', operations: 98, rating: 4.6 },
          { name: 'فاطمة عبدالله الأحمد', role: 'فني صب', operations: 75, rating: 4.7 }
        ],
        topCustomers: [
          { name: 'أحمد محمد علي', operations: 45, revenue: 67500 },
          { name: 'فاطمة أحمد السالم', operations: 38, revenue: 52800 },
          { name: 'محمد عبدالله الأحمد', operations: 52, revenue: 78950 }
        ]
      }
    };

    setReportData(mockReportData[activeReportType] || mockReportData.daily);
    setIsLoading(false);
  };

  const getMonthName = (monthNumber) => {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[monthNumber - 1];
  };

  const handleReportTypeChange = (reportType) => {
    setActiveReportType(reportType);
  };

  const handleDateRangeChange = (field, value) => {
    setDateRange({
      ...dateRange,
      [field]: value
    });
  };

  const handleMonthChange = (field, value) => {
    setSelectedMonth({
      ...selectedMonth,
      [field]: parseInt(value)
    });
  };

  const exportReport = (format) => {
    // محاكاة تصدير التقرير
    const reportContent = JSON.stringify(reportData, null, 2);
    const blob = new Blob([reportContent], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `report_${activeReportType}_${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderDateSelector = () => {
    switch (activeReportType) {
      case 'daily':
        return (
          <div className="form-group">
            <label>التاريخ</label>
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => handleDateRangeChange('startDate', e.target.value)}
            />
          </div>
        );
      
      case 'weekly':
        return (
          <div className="form-group">
            <label>بداية الأسبوع</label>
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => handleDateRangeChange('startDate', e.target.value)}
            />
          </div>
        );
      
      case 'monthly':
        return (
          <div style={{ display: 'flex', gap: '10px' }}>
            <div className="form-group">
              <label>السنة</label>
              <select
                value={selectedMonth.year}
                onChange={(e) => handleMonthChange('year', e.target.value)}
              >
                {[2023, 2024, 2025, 2026].map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>
            <div className="form-group">
              <label>الشهر</label>
              <select
                value={selectedMonth.month}
                onChange={(e) => handleMonthChange('month', e.target.value)}
              >
                {Array.from({ length: 12 }, (_, i) => (
                  <option key={i + 1} value={i + 1}>
                    {getMonthName(i + 1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        );
      
      case 'custom':
        return (
          <div style={{ display: 'flex', gap: '10px' }}>
            <div className="form-group">
              <label>من تاريخ</label>
              <input
                type="date"
                value={dateRange.startDate}
                onChange={(e) => handleDateRangeChange('startDate', e.target.value)}
              />
            </div>
            <div className="form-group">
              <label>إلى تاريخ</label>
              <input
                type="date"
                value={dateRange.endDate}
                onChange={(e) => handleDateRangeChange('endDate', e.target.value)}
              />
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="reports">
      <div className="card">
        <div className="card-header">
          <h1 className="card-title">التقارير والإحصائيات</h1>
          <div style={{ display: 'flex', gap: '10px' }}>
            <button 
              className="btn btn-success"
              onClick={() => exportReport('json')}
            >
              📄 تصدير JSON
            </button>
            <button 
              className="btn btn-primary"
              onClick={() => exportReport('pdf')}
            >
              📑 تصدير PDF
            </button>
          </div>
        </div>

        {/* أنواع التقارير */}
        <div className="tabs" style={{ marginBottom: '20px' }}>
          <div style={{ display: 'flex', borderBottom: '1px solid #e0e0e0' }}>
            {reportTypes.map((type) => (
              <button
                key={type.id}
                className={`tab-button ${activeReportType === type.id ? 'active' : ''}`}
                onClick={() => handleReportTypeChange(type.id)}
                style={{
                  padding: '15px 20px',
                  border: 'none',
                  background: activeReportType === type.id ? '#667eea' : 'transparent',
                  color: activeReportType === type.id ? 'white' : '#666',
                  cursor: 'pointer',
                  borderRadius: '8px 8px 0 0',
                  marginLeft: '5px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                <span>{type.icon}</span>
                {type.label}
              </button>
            ))}
          </div>
        </div>

        {/* محدد التاريخ */}
        <div className="card" style={{ marginBottom: '20px' }}>
          <div className="card-header">
            <h3>إعدادات التقرير</h3>
            <button 
              className="btn btn-primary"
              onClick={generateReport}
            >
              🔄 تحديث التقرير
            </button>
          </div>
          
          <div style={{ display: 'flex', gap: '20px', alignItems: 'end' }}>
            {renderDateSelector()}
          </div>
        </div>

        {/* محتوى التقرير */}
        {reportData && (
          <>
            {/* رأس التقرير */}
            <div className="card" style={{ marginBottom: '20px' }}>
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <h2>🏆 تقرير معمل الذهب</h2>
                <h3>{reportData.period}</h3>
                <p style={{ color: '#666' }}>
                  تم إنشاؤه في: {new Date().toLocaleString('ar-SA')}
                </p>
              </div>
            </div>

            {/* إحصائيات العمليات */}
            <div className="card" style={{ marginBottom: '20px' }}>
              <div className="card-header">
                <h3>📊 إحصائيات العمليات</h3>
              </div>
              
              <div className="stats-grid">
                <div className="stat-card">
                  <div className="stat-value">{reportData.operations.samplesAnalyzed}</div>
                  <div className="stat-label">عينات محللة</div>
                </div>
                
                <div className="stat-card">
                  <div className="stat-value">{reportData.operations.refinementsPerformed}</div>
                  <div className="stat-label">عمليات تنقية</div>
                </div>
                
                <div className="stat-card">
                  <div className="stat-value">{reportData.operations.castingsPerformed}</div>
                  <div className="stat-label">عمليات صب</div>
                </div>
                
                <div className="stat-card">
                  <div className="stat-value">{reportData.operations.totalCastedWeight} غ</div>
                  <div className="stat-label">إجمالي وزن السبائك</div>
                </div>
              </div>
            </div>

            {/* الإحصائيات المالية */}
            <div className="card" style={{ marginBottom: '20px' }}>
              <div className="card-header">
                <h3>💰 الإحصائيات المالية</h3>
              </div>
              
              <div className="stats-grid">
                <div className="stat-card">
                  <div className="stat-value">{reportData.financial.totalInvoices}</div>
                  <div className="stat-label">إجمالي الفواتير</div>
                </div>
                
                <div className="stat-card">
                  <div className="stat-value">{reportData.financial.totalAmount.toLocaleString()} ر.س</div>
                  <div className="stat-label">إجمالي المبالغ</div>
                </div>
                
                <div className="stat-card">
                  <div className="stat-value">{reportData.financial.totalPaid.toLocaleString()} ر.س</div>
                  <div className="stat-label">المبالغ المحصلة</div>
                </div>
                
                <div className="stat-card">
                  <div className="stat-value">{reportData.financial.totalOutstanding.toLocaleString()} ر.س</div>
                  <div className="stat-label">المبالغ المستحقة</div>
                </div>
              </div>
            </div>

            {/* أداء الموظفين */}
            <div className="card" style={{ marginBottom: '20px' }}>
              <div className="card-header">
                <h3>👨‍💼 أداء الموظفين</h3>
              </div>
              
              <div className="table-container">
                <table className="table">
                  <thead>
                    <tr>
                      <th>اسم الموظف</th>
                      <th>الوظيفة</th>
                      <th>عدد العمليات</th>
                      <th>تقييم الأداء</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.employeePerformance.map((employee, index) => (
                      <tr key={index}>
                        <td>{employee.name}</td>
                        <td>{employee.role}</td>
                        <td>{employee.operations}</td>
                        <td>
                          <span style={{
                            color: employee.rating >= 4.5 ? '#28a745' : employee.rating >= 4.0 ? '#ffc107' : '#dc3545',
                            fontWeight: 'bold'
                          }}>
                            ⭐ {employee.rating.toFixed(1)}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* أهم العملاء */}
            <div className="card">
              <div className="card-header">
                <h3>🏅 أهم العملاء</h3>
              </div>
              
              <div className="table-container">
                <table className="table">
                  <thead>
                    <tr>
                      <th>اسم العميل</th>
                      <th>عدد العمليات</th>
                      <th>إجمالي الإيرادات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.topCustomers.map((customer, index) => (
                      <tr key={index}>
                        <td>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                            <span style={{ 
                              fontSize: '20px',
                              color: index === 0 ? '#ffd700' : index === 1 ? '#c0c0c0' : '#cd7f32'
                            }}>
                              {index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉'}
                            </span>
                            {customer.name}
                          </div>
                        </td>
                        <td>{customer.operations}</td>
                        <td>{customer.revenue.toLocaleString()} ر.س</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Reports;

