"""
المتحكم الرئيسي - يربط بين النماذج والواجهات ويحتوي على منطق الأعمال
"""

from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
from database_manager import DatabaseManager
from customer import Customer
from employee import Employee
from operations import Operations
from invoice import Invoice
import hashlib
import os


class MainController:
    """المتحكم الرئيسي للبرنامج"""
    
    def __init__(self, db_path: str = "gold_lab.db"):
        """
        تهيئة المتحكم الرئيسي
        
        Args:
            db_path: مسار قاعدة البيانات
        """
        self.db = DatabaseManager(db_path)
        self.customer_model = Customer(self.db)
        self.employee_model = Employee(self.db)
        self.operations_model = Operations(self.db)
        self.invoice_model = Invoice(self.db)
        self.current_user = None
        
        # إنشاء مستخدم افتراضي إذا لم يوجد
        self._create_default_user()
    
    def _create_default_user(self):
        """إنشاء مستخدم افتراضي للنظام"""
        # التحقق من وجود مستخدمين
        users = self.db.execute_query("SELECT COUNT(*) as count FROM Users")
        if users[0]['count'] == 0:
            # إنشاء مستخدم افتراضي
            password_hash = self._hash_password("admin123")
            self.db.execute_update(
                "INSERT INTO Users (username, password_hash, role) VALUES (?, ?, ?)",
                ("admin", password_hash, "admin")
            )
    
    def _hash_password(self, password: str) -> str:
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate_user(self, username: str, password: str) -> bool:
        """
        تسجيل دخول المستخدم
        
        Args:
            username: اسم المستخدم
            password: كلمة المرور
            
        Returns:
            True إذا تم تسجيل الدخول بنجاح
        """
        password_hash = self._hash_password(password)
        query = "SELECT * FROM Users WHERE username = ? AND password_hash = ?"
        results = self.db.execute_query(query, (username, password_hash))
        
        if results:
            self.current_user = dict(results[0])
            return True
        return False
    
    def logout_user(self):
        """تسجيل خروج المستخدم"""
        self.current_user = None
    
    def is_authenticated(self) -> bool:
        """التحقق من تسجيل دخول المستخدم"""
        return self.current_user is not None
    
    # ==================== إدارة العملاء ====================
    
    def add_customer(self, name: str, id_number: str, phone: str = None, address: str = None) -> Tuple[bool, str, int]:
        """
        إضافة عميل جديد
        
        Returns:
            (نجح, رسالة, معرف العميل)
        """
        try:
            customer_id = self.customer_model.add_customer(name, id_number, phone, address)
            return True, f"تم إضافة العميل بنجاح برقم {customer_id}", customer_id
        except ValueError as e:
            return False, str(e), 0
        except Exception as e:
            return False, f"خطأ في إضافة العميل: {e}", 0
    
    def search_customers(self, search_term: str) -> List[Dict]:
        """البحث عن العملاء"""
        return self.customer_model.search_customers(search_term)
    
    def get_customer_details(self, customer_id: int) -> Optional[Dict]:
        """الحصول على تفاصيل العميل مع العمليات والملخص المالي"""
        customer = self.customer_model.get_customer_by_id(customer_id)
        if not customer:
            return None
        
        operations = self.customer_model.get_customer_operations(customer_id)
        financial_summary = self.customer_model.get_customer_financial_summary(customer_id)
        
        return {
            'customer': customer,
            'operations': operations,
            'financial_summary': financial_summary
        }
    
    # ==================== إدارة الموظفين ====================
    
    def add_employee(self, name: str, id_number: str, role: str, phone: str = None) -> Tuple[bool, str, int]:
        """
        إضافة موظف جديد
        
        Returns:
            (نجح, رسالة, معرف الموظف)
        """
        try:
            employee_id = self.employee_model.add_employee(name, id_number, role, phone)
            return True, f"تم إضافة الموظف بنجاح برقم {employee_id}", employee_id
        except ValueError as e:
            return False, str(e), 0
        except Exception as e:
            return False, f"خطأ في إضافة الموظف: {e}", 0
    
    def get_employees_by_role(self, role: str) -> List[Dict]:
        """الحصول على الموظفين حسب الوظيفة"""
        return self.employee_model.get_employees_by_role(role)
    
    # ==================== إدارة العمليات ====================
    
    def add_sample_analysis(self, customer_id: int, weight: float, material_type: str, 
                           employee_id: int = None) -> Tuple[bool, str, int]:
        """
        إضافة عينة للتحليل
        
        Returns:
            (نجح, رسالة, معرف العينة)
        """
        try:
            sample_id = self.operations_model.add_sample(customer_id, weight, material_type, employee_id)
            return True, f"تم إضافة العينة بنجاح برقم {sample_id}", sample_id
        except Exception as e:
            return False, f"خطأ في إضافة العينة: {e}", 0
    
    def complete_sample_analysis(self, sample_id: int, purity_percentage: float, 
                                impurities: str = None, employee_id: int = None) -> Tuple[bool, str]:
        """
        إكمال تحليل العينة
        
        Returns:
            (نجح, رسالة)
        """
        try:
            success = self.operations_model.update_sample_results(sample_id, purity_percentage, impurities, employee_id)
            if success:
                return True, "تم تحديث نتائج التحليل بنجاح"
            else:
                return False, "فشل في تحديث نتائج التحليل"
        except Exception as e:
            return False, f"خطأ في تحديث التحليل: {e}"
    
    def add_refinement_process(self, sample_id: int, raw_weight: float, 
                              chemicals_used: str = None, employee_id: int = None) -> Tuple[bool, str, int]:
        """
        إضافة عملية تنقية
        
        Returns:
            (نجح, رسالة, معرف عملية التنقية)
        """
        try:
            refinement_id = self.operations_model.add_refinement(sample_id, raw_weight, chemicals_used, employee_id)
            return True, f"تم إضافة عملية التنقية بنجاح برقم {refinement_id}", refinement_id
        except Exception as e:
            return False, f"خطأ في إضافة عملية التنقية: {e}", 0
    
    def complete_refinement_process(self, refinement_id: int, refined_weight: float, 
                                   employee_id: int = None) -> Tuple[bool, str]:
        """
        إكمال عملية التنقية
        
        Returns:
            (نجح, رسالة)
        """
        try:
            success = self.operations_model.update_refinement_results(refinement_id, refined_weight, None, employee_id)
            if success:
                return True, "تم تحديث نتائج التنقية بنجاح"
            else:
                return False, "فشل في تحديث نتائج التنقية"
        except Exception as e:
            return False, f"خطأ في تحديث التنقية: {e}"
    
    def add_casting_process(self, customer_id: int, bar_weight: float, bar_shape: str,
                           purity_percentage: float, refinement_id: int = None, 
                           employee_id: int = None) -> Tuple[bool, str, int]:
        """
        إضافة عملية صب
        
        Returns:
            (نجح, رسالة, معرف عملية الصب)
        """
        try:
            casting_id = self.operations_model.add_casting(
                customer_id, bar_weight, bar_shape, purity_percentage, refinement_id, employee_id
            )
            return True, f"تم إضافة عملية الصب بنجاح برقم {casting_id}", casting_id
        except Exception as e:
            return False, f"خطأ في إضافة عملية الصب: {e}", 0
    
    def get_pending_operations(self) -> Dict:
        """الحصول على العمليات المعلقة"""
        return self.operations_model.get_pending_operations()
    
    # ==================== إدارة الفواتير ====================
    
    def create_sample_invoice(self, sample_id: int, paid_amount: float = 0) -> Tuple[bool, str, int]:
        """
        إنشاء فاتورة لعينة تحليل
        
        Returns:
            (نجح, رسالة, معرف الفاتورة)
        """
        try:
            invoice_id = self.invoice_model.create_invoice_for_sample(sample_id, paid_amount)
            return True, f"تم إنشاء الفاتورة بنجاح برقم {invoice_id}", invoice_id
        except Exception as e:
            return False, f"خطأ في إنشاء الفاتورة: {e}", 0
    
    def create_casting_invoice(self, casting_id: int, casting_cost: float, paid_amount: float = 0) -> Tuple[bool, str, int]:
        """
        إنشاء فاتورة لعملية صب
        
        Returns:
            (نجح, رسالة, معرف الفاتورة)
        """
        try:
            invoice_id = self.invoice_model.create_invoice_for_casting(casting_id, casting_cost, paid_amount)
            return True, f"تم إنشاء الفاتورة بنجاح برقم {invoice_id}", invoice_id
        except Exception as e:
            return False, f"خطأ في إنشاء الفاتورة: {e}", 0
    
    def add_payment_to_invoice(self, invoice_id: int, payment_amount: float) -> Tuple[bool, str]:
        """
        إضافة دفعة لفاتورة
        
        Returns:
            (نجح, رسالة)
        """
        try:
            success = self.invoice_model.add_payment(invoice_id, payment_amount)
            if success:
                return True, "تم إضافة الدفعة بنجاح"
            else:
                return False, "فشل في إضافة الدفعة"
        except Exception as e:
            return False, f"خطأ في إضافة الدفعة: {e}"
    
    def get_unpaid_invoices(self) -> List[Dict]:
        """الحصول على الفواتير غير المدفوعة"""
        return self.invoice_model.get_unpaid_invoices()
    
    # ==================== التقارير ====================
    
    def get_daily_report(self, date: str = None) -> Dict:
        """
        الحصول على تقرير يومي
        
        Args:
            date: التاريخ (YYYY-MM-DD) - افتراضي اليوم
            
        Returns:
            تقرير يومي
        """
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        operations_summary = self.operations_model.get_operations_summary(date, date)
        financial_summary = self.invoice_model.get_financial_summary(date, date)
        
        return {
            'date': date,
            'operations': operations_summary,
            'financial': financial_summary
        }
    
    def get_weekly_report(self, start_date: str = None) -> Dict:
        """
        الحصول على تقرير أسبوعي
        
        Args:
            start_date: تاريخ بداية الأسبوع (YYYY-MM-DD) - افتراضي بداية الأسبوع الحالي
            
        Returns:
            تقرير أسبوعي
        """
        if start_date is None:
            today = datetime.now()
            start_date = (today - timedelta(days=today.weekday())).strftime('%Y-%m-%d')
        
        end_date = (datetime.strptime(start_date, '%Y-%m-%d') + timedelta(days=6)).strftime('%Y-%m-%d')
        
        operations_summary = self.operations_model.get_operations_summary(start_date, end_date)
        financial_summary = self.invoice_model.get_financial_summary(start_date, end_date)
        employee_performance = self.employee_model.get_all_employees_performance(start_date, end_date)
        
        return {
            'start_date': start_date,
            'end_date': end_date,
            'operations': operations_summary,
            'financial': financial_summary,
            'employee_performance': employee_performance
        }
    
    def get_monthly_report(self, year: int = None, month: int = None) -> Dict:
        """
        الحصول على تقرير شهري
        
        Args:
            year: السنة - افتراضي السنة الحالية
            month: الشهر - افتراضي الشهر الحالي
            
        Returns:
            تقرير شهري
        """
        if year is None or month is None:
            today = datetime.now()
            year = year or today.year
            month = month or today.month
        
        start_date = f"{year}-{month:02d}-01"
        
        # حساب آخر يوم في الشهر
        if month == 12:
            next_month = datetime(year + 1, 1, 1)
        else:
            next_month = datetime(year, month + 1, 1)
        
        end_date = (next_month - timedelta(days=1)).strftime('%Y-%m-%d')
        
        operations_summary = self.operations_model.get_operations_summary(start_date, end_date)
        financial_summary = self.invoice_model.get_financial_summary(start_date, end_date)
        employee_performance = self.employee_model.get_all_employees_performance(start_date, end_date)
        
        return {
            'year': year,
            'month': month,
            'start_date': start_date,
            'end_date': end_date,
            'operations': operations_summary,
            'financial': financial_summary,
            'employee_performance': employee_performance
        }
    
    def get_dashboard_data(self) -> Dict:
        """
        الحصول على بيانات لوحة التحكم الرئيسية
        
        Returns:
            بيانات لوحة التحكم
        """
        # إحصائيات اليوم
        today_report = self.get_daily_report()
        
        # العمليات المعلقة
        pending_operations = self.get_pending_operations()
        
        # الفواتير غير المدفوعة
        unpaid_invoices = self.get_unpaid_invoices()
        
        # إحصائيات عامة
        total_customers = len(self.customer_model.get_all_customers())
        total_employees = len(self.employee_model.get_all_employees())
        
        return {
            'today_report': today_report,
            'pending_operations': pending_operations,
            'unpaid_invoices': unpaid_invoices,
            'total_customers': total_customers,
            'total_employees': total_employees
        }
    
    def backup_database(self, backup_path: str = None) -> Tuple[bool, str]:
        """
        إنشاء نسخة احتياطية من قاعدة البيانات
        
        Args:
            backup_path: مسار النسخة الاحتياطية
            
        Returns:
            (نجح, رسالة)
        """
        if backup_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = f"backup_gold_lab_{timestamp}.db"
        
        try:
            success = self.db.backup_database(backup_path)
            if success:
                return True, f"تم إنشاء النسخة الاحتياطية بنجاح: {backup_path}"
            else:
                return False, "فشل في إنشاء النسخة الاحتياطية"
        except Exception as e:
            return False, f"خطأ في إنشاء النسخة الاحتياطية: {e}"
    
    def __del__(self):
        """تنظيف الموارد"""
        if hasattr(self, 'db'):
            self.db.disconnect()

