# دليل تطبيق سطح المكتب - نظام إدارة معمل الذهب

## 🖥️ نظرة عامة

تطبيق سطح المكتب هو الواجهة الرسومية المتقدمة لنظام إدارة معمل الذهب، مصمم باستخدام Python Tkinter مع دعم كامل للغة العربية.

## 🚀 طرق التشغيل

### الطريقة الأولى: التشغيل المباشر
```bash
# النقر المزدوج على الملف
start_desktop.bat

# أو من سطر الأوامر
python start_desktop.py
```

### الطريقة الثانية: مع خيارات متقدمة
```bash
# النقر المزدوج على الملف
run_desktop.bat

# أو من سطر الأوامر
python run_desktop.py
```

### الطريقة الثالثة: تشغيل التطبيق مباشرة
```bash
python desktop_app.py
```

## 🔐 تسجيل الدخول

### بيانات الدخول الافتراضية
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

### تغيير كلمة المرور
يمكن تغيير كلمة المرور من خلال:
1. تسجيل الدخول بالبيانات الافتراضية
2. الذهاب إلى قائمة "ملف" → "تغيير كلمة المرور"

## 📱 واجهة المستخدم

### لوحة التحكم الرئيسية
- **إجمالي العملاء**: عدد العملاء المسجلين
- **إجمالي الموظفين**: عدد الموظفين
- **العمليات النشطة**: العمليات الجارية
- **إجمالي الفواتير**: عدد الفواتير

### الشريط الجانبي
- 🏠 **لوحة التحكم**: الصفحة الرئيسية مع الإحصائيات
- 👥 **العملاء**: إدارة بيانات العملاء
- 👨‍💼 **الموظفين**: إدارة بيانات الموظفين
- ⚙️ **العمليات**: إدارة عمليات المعمل
- 💰 **الفواتير**: إدارة الفواتير والمدفوعات
- 📈 **التقارير**: عرض التقارير والإحصائيات

## 👥 إدارة العملاء

### إضافة عميل جديد
1. انقر على "العملاء" في الشريط الجانبي
2. انقر على زر "إضافة عميل جديد"
3. املأ البيانات المطلوبة:
   - **اسم العميل** (مطلوب)
   - **رقم الهوية** (مطلوب)
   - **رقم الهاتف** (اختياري)
   - **العنوان** (اختياري)
4. انقر على "حفظ"

### تعديل عميل
- **النقر المزدوج** على العميل في الجدول
- أو **النقر بالزر الأيمن** واختيار "تعديل"

### حذف عميل
- **النقر بالزر الأيمن** على العميل واختيار "حذف"
- تأكيد عملية الحذف

### البحث في العملاء
- استخدم مربع البحث أعلى الجدول
- البحث يشمل الاسم ورقم الهوية والهاتف

## 👨‍💼 إدارة الموظفين

### إضافة موظف جديد
1. انقر على "الموظفين" في الشريط الجانبي
2. انقر على زر "إضافة موظف جديد"
3. املأ البيانات المطلوبة:
   - **اسم الموظف** (مطلوب)
   - **رقم الهوية** (مطلوب)
   - **المنصب** (مطلوب): محلل، فني تنقية، فني صب، مشرف، مدير
   - **رقم الهاتف** (اختياري)
4. انقر على "حفظ"

### المناصب المتاحة
- **محلل**: متخصص في تحليل العينات
- **فني تنقية**: متخصص في عمليات التنقية
- **فني صب**: متخصص في عمليات الصب
- **مشرف**: مشرف على العمليات
- **مدير**: إدارة عامة

## ⚙️ العمليات والفواتير والتقارير

هذه الأقسام قيد التطوير وستكون متاحة في التحديثات القادمة.

## 🎨 الميزات المتقدمة

### الواجهة العربية
- دعم كامل للغة العربية
- تخطيط من اليمين إلى اليسار (RTL)
- خطوط عربية واضحة

### التفاعل
- **النقر المزدوج**: تعديل العنصر
- **النقر بالزر الأيمن**: قائمة السياق
- **مفاتيح الاختصار**:
  - `Enter`: حفظ في الحوارات
  - `Escape`: إلغاء في الحوارات

### الجداول التفاعلية
- فرز البيانات بالنقر على عناوين الأعمدة
- شريط تمرير أفقي وعمودي
- تحديد متعدد للعناصر

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

1. **التطبيق لا يفتح**
   - تأكد من تثبيت Python 3.8 أو أحدث
   - تأكد من وجود جميع الملفات المطلوبة

2. **خطأ في قاعدة البيانات**
   - احذف ملف `gold_lab.db` وأعد تشغيل التطبيق
   - سيتم إنشاء قاعدة بيانات جديدة تلقائياً

3. **مشاكل في الخطوط العربية**
   - تأكد من وجود خطوط عربية على النظام
   - التطبيق يستخدم خط Arial افتراضياً

## 📞 الدعم الفني

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من ملف `README.md` للمعلومات التقنية
3. تأكد من تحديث النظام لآخر إصدار

## 🔄 التحديثات المستقبلية

### قيد التطوير
- إدارة العمليات الكاملة
- نظام الفواتير المتقدم
- تقارير مفصلة مع الرسوم البيانية
- نظام النسخ الاحتياطي التلقائي
- إعدادات متقدمة للمستخدم

---

**نظام إدارة معمل الذهب - تطبيق سطح المكتب**  
الإصدار 1.0.0 - 2025
