import React from 'react';

const Header = ({ currentUser, onToggleSidebar, onLogout }) => {
  const getCurrentTime = () => {
    return new Date().toLocaleString('ar-SA', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getUserInitials = (name) => {
    if (!name) return 'م';
    const words = name.split(' ');
    if (words.length >= 2) {
      return words[0][0] + words[1][0];
    }
    return name[0];
  };

  return (
    <header className="header">
      <div className="header-left">
        <button className="menu-toggle" onClick={onToggleSidebar}>
          ☰
        </button>
        <div className="current-time">
          {getCurrentTime()}
        </div>
      </div>
      
      <div className="header-right">
        <div className="user-info">
          <div className="user-avatar">
            {getUserInitials(currentUser?.name)}
          </div>
          <div className="user-details">
            <div className="user-name">{currentUser?.name || 'مستخدم'}</div>
            <div className="user-role" style={{ fontSize: '12px', color: '#666' }}>
              {currentUser?.role === 'admin' ? 'مدير النظام' : 'مستخدم'}
            </div>
          </div>
        </div>
        
        <button className="logout-btn" onClick={onLogout}>
          تسجيل الخروج
        </button>
      </div>
    </header>
  );
};

export default Header;

