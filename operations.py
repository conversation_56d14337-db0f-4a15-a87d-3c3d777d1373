"""
نموذج العمليات - يحتوي على جميع العمليات المتعلقة بالتحليل والتنقية والصب
"""

from typing import List, Dict, Optional
from datetime import datetime
from database_manager import DatabaseManager
import uuid


class Operations:
    """نموذج العمليات"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        تهيئة نموذج العمليات
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db = db_manager
    
    # ==================== عمليات التحليل ====================
    
    def add_sample(self, customer_id: int, weight: float, material_type: str, 
                   employee_id: int = None, analysis_cost: float = None) -> int:
        """
        إضافة عينة جديدة للتحليل
        
        Args:
            customer_id: معرف العميل
            weight: وزن العينة بالغرام
            material_type: نوع المادة (ذهب خام، سبيكة، مجوهرات)
            employee_id: معرف الموظف المحلل (اختياري)
            analysis_cost: تكلفة التحليل (اختياري)
            
        Returns:
            معرف العينة الجديدة
        """
        # حساب تكلفة التحليل إذا لم تُمرر
        if analysis_cost is None:
            analysis_cost = self.calculate_analysis_cost(weight, material_type)
        
        query = """
            INSERT INTO Samples (customer_id, weight, material_type, analysis_cost, employee_id)
            VALUES (?, ?, ?, ?, ?)
        """
        return self.db.execute_update(query, (customer_id, weight, material_type, analysis_cost, employee_id))
    
    def update_sample_results(self, sample_id: int, purity_percentage: float, 
                             impurities: str = None, employee_id: int = None) -> bool:
        """
        تحديث نتائج تحليل العينة
        
        Args:
            sample_id: معرف العينة
            purity_percentage: نسبة النقاء
            impurities: وصف الشوائب (اختياري)
            employee_id: معرف الموظف المحلل (اختياري)
            
        Returns:
            True إذا تم التحديث بنجاح
        """
        updates = ["purity_percentage = ?"]
        params = [purity_percentage]
        
        if impurities is not None:
            updates.append("impurities = ?")
            params.append(impurities)
        
        if employee_id is not None:
            updates.append("employee_id = ?")
            params.append(employee_id)
        
        params.append(sample_id)
        query = f"UPDATE Samples SET {', '.join(updates)} WHERE sample_id = ?"
        
        rows_affected = self.db.execute_update(query, tuple(params))
        return rows_affected > 0
    
    def get_sample_by_id(self, sample_id: int) -> Optional[Dict]:
        """
        الحصول على عينة بواسطة المعرف
        
        Args:
            sample_id: معرف العينة
            
        Returns:
            بيانات العينة مع معلومات العميل والموظف
        """
        query = """
            SELECT s.*, c.name as customer_name, c.id_number as customer_id_number,
                   e.name as employee_name, e.role as employee_role
            FROM Samples s
            LEFT JOIN Customers c ON s.customer_id = c.customer_id
            LEFT JOIN Employees e ON s.employee_id = e.employee_id
            WHERE s.sample_id = ?
        """
        results = self.db.execute_query(query, (sample_id,))
        return dict(results[0]) if results else None
    
    def get_samples_by_customer(self, customer_id: int) -> List[Dict]:
        """
        الحصول على جميع عينات عميل معين
        
        Args:
            customer_id: معرف العميل
            
        Returns:
            قائمة بعينات العميل
        """
        query = """
            SELECT s.*, e.name as employee_name
            FROM Samples s
            LEFT JOIN Employees e ON s.employee_id = e.employee_id
            WHERE s.customer_id = ?
            ORDER BY s.analysis_date DESC
        """
        results = self.db.execute_query(query, (customer_id,))
        return [dict(row) for row in results]
    
    def calculate_analysis_cost(self, weight: float, material_type: str) -> float:
        """
        حساب تكلفة التحليل بناءً على الوزن ونوع المادة
        
        Args:
            weight: وزن العينة
            material_type: نوع المادة
            
        Returns:
            تكلفة التحليل
        """
        # أسعار التحليل (يمكن تخصيصها)
        base_prices = {
            'ذهب خام': 50.0,
            'سبيكة': 30.0,
            'مجوهرات': 40.0,
            'أخرى': 35.0
        }
        
        base_price = base_prices.get(material_type, 35.0)
        
        # إضافة تكلفة إضافية للأوزان الكبيرة
        if weight > 100:
            base_price += (weight - 100) * 0.1
        
        return round(base_price, 2)
    
    # ==================== عمليات التنقية ====================
    
    def add_refinement(self, sample_id: int, raw_weight: float, chemicals_used: str = None,
                      employee_id: int = None) -> int:
        """
        إضافة عملية تنقية جديدة
        
        Args:
            sample_id: معرف العينة
            raw_weight: وزن الذهب الخام قبل التنقية
            chemicals_used: المواد الكيميائية المستخدمة (اختياري)
            employee_id: معرف الموظف (اختياري)
            
        Returns:
            معرف عملية التنقية الجديدة
        """
        query = """
            INSERT INTO Refinements (sample_id, raw_weight, chemicals_used, employee_id)
            VALUES (?, ?, ?, ?)
        """
        return self.db.execute_update(query, (sample_id, raw_weight, chemicals_used, employee_id))
    
    def update_refinement_results(self, refinement_id: int, refined_weight: float,
                                 loss_weight: float = None, employee_id: int = None) -> bool:
        """
        تحديث نتائج عملية التنقية
        
        Args:
            refinement_id: معرف عملية التنقية
            refined_weight: وزن الذهب بعد التنقية
            loss_weight: وزن الخسائر (اختياري - سيتم حسابه تلقائياً)
            employee_id: معرف الموظف (اختياري)
            
        Returns:
            True إذا تم التحديث بنجاح
        """
        # الحصول على الوزن الخام لحساب الخسائر
        refinement = self.get_refinement_by_id(refinement_id)
        if not refinement:
            return False
        
        if loss_weight is None:
            loss_weight = refinement['raw_weight'] - refined_weight
        
        # حساب نسبة الاسترداد
        recovery_percentage = (refined_weight / refinement['raw_weight']) * 100 if refinement['raw_weight'] > 0 else 0
        
        updates = ["refined_weight = ?", "loss_weight = ?", "recovery_percentage = ?"]
        params = [refined_weight, loss_weight, recovery_percentage]
        
        if employee_id is not None:
            updates.append("employee_id = ?")
            params.append(employee_id)
        
        params.append(refinement_id)
        query = f"UPDATE Refinements SET {', '.join(updates)} WHERE refinement_id = ?"
        
        rows_affected = self.db.execute_update(query, tuple(params))
        return rows_affected > 0
    
    def get_refinement_by_id(self, refinement_id: int) -> Optional[Dict]:
        """
        الحصول على عملية تنقية بواسطة المعرف
        
        Args:
            refinement_id: معرف عملية التنقية
            
        Returns:
            بيانات عملية التنقية مع معلومات العينة والموظف
        """
        query = """
            SELECT r.*, s.customer_id, s.weight as sample_weight, s.material_type,
                   c.name as customer_name, e.name as employee_name
            FROM Refinements r
            LEFT JOIN Samples s ON r.sample_id = s.sample_id
            LEFT JOIN Customers c ON s.customer_id = c.customer_id
            LEFT JOIN Employees e ON r.employee_id = e.employee_id
            WHERE r.refinement_id = ?
        """
        results = self.db.execute_query(query, (refinement_id,))
        return dict(results[0]) if results else None
    
    # ==================== عمليات الصب ====================
    
    def add_casting(self, customer_id: int, bar_weight: float, bar_shape: str,
                   purity_percentage: float, refinement_id: int = None, employee_id: int = None) -> int:
        """
        إضافة عملية صب جديدة
        
        Args:
            customer_id: معرف العميل
            bar_weight: وزن السبيكة
            bar_shape: شكل السبيكة (مستطيل، دائري، مربع)
            purity_percentage: نسبة النقاء
            refinement_id: معرف عملية التنقية (اختياري)
            employee_id: معرف الموظف (اختياري)
            
        Returns:
            معرف عملية الصب الجديدة
        """
        # توليد رقم تسلسلي فريد للسبيكة
        serial_number = self.generate_serial_number()
        
        query = """
            INSERT INTO Castings (customer_id, bar_weight, bar_shape, purity_percentage, 
                                serial_number, refinement_id, employee_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        return self.db.execute_update(query, (customer_id, bar_weight, bar_shape, 
                                            purity_percentage, serial_number, refinement_id, employee_id))
    
    def get_casting_by_id(self, casting_id: int) -> Optional[Dict]:
        """
        الحصول على عملية صب بواسطة المعرف
        
        Args:
            casting_id: معرف عملية الصب
            
        Returns:
            بيانات عملية الصب مع معلومات العميل والموظف
        """
        query = """
            SELECT c.*, cust.name as customer_name, cust.id_number as customer_id_number,
                   e.name as employee_name, r.refined_weight
            FROM Castings c
            LEFT JOIN Customers cust ON c.customer_id = cust.customer_id
            LEFT JOIN Employees e ON c.employee_id = e.employee_id
            LEFT JOIN Refinements r ON c.refinement_id = r.refinement_id
            WHERE c.casting_id = ?
        """
        results = self.db.execute_query(query, (casting_id,))
        return dict(results[0]) if results else None
    
    def get_casting_by_serial(self, serial_number: str) -> Optional[Dict]:
        """
        الحصول على عملية صب بواسطة الرقم التسلسلي
        
        Args:
            serial_number: الرقم التسلسلي للسبيكة
            
        Returns:
            بيانات عملية الصب
        """
        query = """
            SELECT c.*, cust.name as customer_name, e.name as employee_name
            FROM Castings c
            LEFT JOIN Customers cust ON c.customer_id = cust.customer_id
            LEFT JOIN Employees e ON c.employee_id = e.employee_id
            WHERE c.serial_number = ?
        """
        results = self.db.execute_query(query, (serial_number,))
        return dict(results[0]) if results else None
    
    def generate_serial_number(self) -> str:
        """
        توليد رقم تسلسلي فريد للسبيكة
        
        Returns:
            رقم تسلسلي فريد
        """
        # تنسيق: GL-YYYY-NNNNNN (GL = Gold Lab, YYYY = السنة, NNNNNN = رقم تسلسلي)
        current_year = datetime.now().year
        
        # الحصول على آخر رقم تسلسلي لهذا العام
        query = """
            SELECT serial_number FROM Castings 
            WHERE serial_number LIKE ? 
            ORDER BY casting_id DESC LIMIT 1
        """
        pattern = f"GL-{current_year}-%"
        results = self.db.execute_query(query, (pattern,))
        
        if results:
            last_serial = results[0]['serial_number']
            # استخراج الرقم التسلسلي وزيادته
            last_number = int(last_serial.split('-')[2])
            new_number = last_number + 1
        else:
            new_number = 1
        
        return f"GL-{current_year}-{new_number:06d}"
    
    # ==================== تقارير العمليات ====================
    
    def get_operations_summary(self, start_date: str = None, end_date: str = None) -> Dict:
        """
        الحصول على ملخص العمليات لفترة معينة
        
        Args:
            start_date: تاريخ البداية (YYYY-MM-DD)
            end_date: تاريخ النهاية (YYYY-MM-DD)
            
        Returns:
            ملخص العمليات
        """
        date_filter = ""
        params = []
        
        if start_date and end_date:
            date_filter = " WHERE DATE(analysis_date) BETWEEN ? AND ?"
            params = [start_date, end_date]
        
        # عدد العينات المحللة
        samples_query = f"SELECT COUNT(*) as count FROM Samples{date_filter}"
        samples_count = self.db.execute_query(samples_query, params)[0]['count']
        
        # عدد عمليات التنقية
        refinements_query = f"SELECT COUNT(*) as count FROM Refinements{date_filter.replace('analysis_date', 'refinement_date')}"
        refinements_count = self.db.execute_query(refinements_query, params)[0]['count']
        
        # عدد عمليات الصب
        castings_query = f"SELECT COUNT(*) as count FROM Castings{date_filter.replace('analysis_date', 'casting_date')}"
        castings_count = self.db.execute_query(castings_query, params)[0]['count']
        
        # إجمالي وزن السبائك المصبوبة
        total_weight_query = f"SELECT SUM(bar_weight) as total FROM Castings{date_filter.replace('analysis_date', 'casting_date')}"
        total_weight = self.db.execute_query(total_weight_query, params)[0]['total'] or 0
        
        return {
            'samples_analyzed': samples_count,
            'refinements_performed': refinements_count,
            'castings_performed': castings_count,
            'total_casted_weight': round(total_weight, 2)
        }
    
    def get_pending_operations(self) -> Dict:
        """
        الحصول على العمليات المعلقة (غير المكتملة)
        
        Returns:
            قاموس بالعمليات المعلقة
        """
        # العينات بدون نتائج تحليل
        pending_samples = self.db.execute_query("""
            SELECT s.*, c.name as customer_name
            FROM Samples s
            LEFT JOIN Customers c ON s.customer_id = c.customer_id
            WHERE s.purity_percentage IS NULL
            ORDER BY s.analysis_date
        """)
        
        # عمليات التنقية بدون نتائج
        pending_refinements = self.db.execute_query("""
            SELECT r.*, s.customer_id, c.name as customer_name
            FROM Refinements r
            LEFT JOIN Samples s ON r.sample_id = s.sample_id
            LEFT JOIN Customers c ON s.customer_id = c.customer_id
            WHERE r.refined_weight IS NULL
            ORDER BY r.refinement_date
        """)
        
        return {
            'pending_samples': [dict(row) for row in pending_samples],
            'pending_refinements': [dict(row) for row in pending_refinements]
        }

