import React, { useState, useEffect } from 'react';

const OperationsManagement = () => {
  const [activeTab, setActiveTab] = useState('samples');
  const [operations, setOperations] = useState({
    samples: [],
    refinements: [],
    castings: []
  });
  const [customers, setCustomers] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({});
  const [message, setMessage] = useState({ type: '', text: '' });

  const tabs = [
    { id: 'samples', label: 'تحليل العينات', icon: '🔬' },
    { id: 'refinements', label: 'التنقية', icon: '⚗️' },
    { id: 'castings', label: 'الصب', icon: '🏭' }
  ];

  const materialTypes = [
    'ذهب خام',
    'سبيكة',
    'مجوهرات',
    'أخرى'
  ];

  const barShapes = [
    'مستطيل',
    'دائري',
    'مربع',
    'بيضاوي'
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    
    // محاكاة استدعاء API
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // بيانات تجريبية
    const mockCustomers = [
      { customer_id: 1, name: 'أحمد محمد علي' },
      { customer_id: 2, name: 'فاطمة أحمد السالم' },
      { customer_id: 3, name: 'محمد عبدالله الأحمد' }
    ];

    const mockEmployees = [
      { employee_id: 1, name: 'سارة أحمد المحمد', role: 'محلل' },
      { employee_id: 2, name: 'محمد علي السالم', role: 'فني تنقية' },
      { employee_id: 3, name: 'فاطمة عبدالله الأحمد', role: 'فني صب' }
    ];

    const mockOperations = {
      samples: [
        {
          sample_id: 1,
          customer_name: 'أحمد محمد علي',
          weight: 25.5,
          material_type: 'ذهب خام',
          purity_percentage: 92.5,
          analysis_cost: 150,
          analysis_date: '2025-07-01',
          employee_name: 'سارة أحمد المحمد',
          status: 'مكتمل'
        },
        {
          sample_id: 2,
          customer_name: 'فاطمة أحمد السالم',
          weight: 18.2,
          material_type: 'مجوهرات',
          purity_percentage: null,
          analysis_cost: 120,
          analysis_date: '2025-07-01',
          employee_name: 'سارة أحمد المحمد',
          status: 'قيد التحليل'
        }
      ],
      refinements: [
        {
          refinement_id: 1,
          sample_id: 1,
          customer_name: 'أحمد محمد علي',
          raw_weight: 25.5,
          refined_weight: 23.6,
          loss_weight: 1.9,
          recovery_percentage: 92.5,
          chemicals_used: 'حمض النيتريك، حمض الهيدروكلوريك',
          refinement_date: '2025-07-01',
          employee_name: 'محمد علي السالم',
          status: 'مكتمل'
        }
      ],
      castings: [
        {
          casting_id: 1,
          customer_name: 'أحمد محمد علي',
          bar_weight: 23.6,
          bar_shape: 'مستطيل',
          purity_percentage: 99.9,
          serial_number: 'GL-2025-000001',
          casting_date: '2025-07-01',
          employee_name: 'فاطمة عبدالله الأحمد',
          status: 'مكتمل'
        }
      ]
    };

    setCustomers(mockCustomers);
    setEmployees(mockEmployees);
    setOperations(mockOperations);
    setIsLoading(false);
  };

  const initializeFormData = (tab) => {
    switch (tab) {
      case 'samples':
        return {
          customer_id: '',
          weight: '',
          material_type: '',
          employee_id: ''
        };
      case 'refinements':
        return {
          sample_id: '',
          raw_weight: '',
          chemicals_used: '',
          employee_id: ''
        };
      case 'castings':
        return {
          customer_id: '',
          bar_weight: '',
          bar_shape: '',
          purity_percentage: '',
          employee_id: ''
        };
      default:
        return {};
    }
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setShowAddForm(false);
    setFormData(initializeFormData(tab));
  };

  const handleAddNew = () => {
    setFormData(initializeFormData(activeTab));
    setShowAddForm(true);
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      // محاكاة استدعاء API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newId = operations[activeTab].length + 1;
      const customerName = customers.find(c => c.customer_id == formData.customer_id)?.name || '';
      const employeeName = employees.find(e => e.employee_id == formData.employee_id)?.name || '';
      
      let newOperation = {
        ...formData,
        [`${activeTab.slice(0, -1)}_id`]: newId,
        customer_name: customerName,
        employee_name: employeeName,
        [`${activeTab.slice(0, -1)}_date`]: new Date().toISOString().split('T')[0],
        status: 'قيد المعالجة'
      };

      // إضافة حقول خاصة بكل نوع عملية
      if (activeTab === 'samples') {
        newOperation.analysis_cost = calculateAnalysisCost(formData.weight, formData.material_type);
      } else if (activeTab === 'castings') {
        newOperation.serial_number = generateSerialNumber();
      }

      setOperations({
        ...operations,
        [activeTab]: [...operations[activeTab], newOperation]
      });

      setMessage({ type: 'success', text: 'تم إضافة العملية بنجاح' });
      setShowAddForm(false);
      setFormData(initializeFormData(activeTab));
    } catch (error) {
      setMessage({ type: 'error', text: 'حدث خطأ أثناء إضافة العملية' });
    }
  };

  const calculateAnalysisCost = (weight, materialType) => {
    const basePrices = {
      'ذهب خام': 50,
      'سبيكة': 30,
      'مجوهرات': 40,
      'أخرى': 35
    };
    
    const basePrice = basePrices[materialType] || 35;
    const weightFactor = parseFloat(weight) > 100 ? (parseFloat(weight) - 100) * 0.1 : 0;
    
    return Math.round(basePrice + weightFactor);
  };

  const generateSerialNumber = () => {
    const year = new Date().getFullYear();
    const number = operations.castings.length + 1;
    return `GL-${year}-${number.toString().padStart(6, '0')}`;
  };

  const updateOperationStatus = async (operationType, operationId, newStatus) => {
    try {
      // محاكاة استدعاء API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setOperations({
        ...operations,
        [operationType]: operations[operationType].map(op => 
          op[`${operationType.slice(0, -1)}_id`] === operationId 
            ? { ...op, status: newStatus }
            : op
        )
      });

      setMessage({ type: 'success', text: 'تم تحديث حالة العملية بنجاح' });
    } catch (error) {
      setMessage({ type: 'error', text: 'حدث خطأ أثناء تحديث العملية' });
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'مكتمل':
        return '#28a745';
      case 'قيد التحليل':
      case 'قيد المعالجة':
        return '#ffc107';
      case 'معلق':
        return '#dc3545';
      default:
        return '#6c757d';
    }
  };

  const renderFormFields = () => {
    switch (activeTab) {
      case 'samples':
        return (
          <>
            <div className="form-row">
              <div className="form-col">
                <div className="form-group">
                  <label>العميل *</label>
                  <select
                    name="customer_id"
                    value={formData.customer_id}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">اختر العميل</option>
                    {customers.map(customer => (
                      <option key={customer.customer_id} value={customer.customer_id}>
                        {customer.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="form-col">
                <div className="form-group">
                  <label>الوزن (غرام) *</label>
                  <input
                    type="number"
                    name="weight"
                    value={formData.weight}
                    onChange={handleInputChange}
                    required
                    step="0.1"
                    placeholder="أدخل الوزن"
                  />
                </div>
              </div>
            </div>
            
            <div className="form-row">
              <div className="form-col">
                <div className="form-group">
                  <label>نوع المادة *</label>
                  <select
                    name="material_type"
                    value={formData.material_type}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">اختر نوع المادة</option>
                    {materialTypes.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="form-col">
                <div className="form-group">
                  <label>المحلل</label>
                  <select
                    name="employee_id"
                    value={formData.employee_id}
                    onChange={handleInputChange}
                  >
                    <option value="">اختر المحلل</option>
                    {employees.filter(e => e.role === 'محلل').map(employee => (
                      <option key={employee.employee_id} value={employee.employee_id}>
                        {employee.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </>
        );

      case 'refinements':
        return (
          <>
            <div className="form-row">
              <div className="form-col">
                <div className="form-group">
                  <label>العينة *</label>
                  <select
                    name="sample_id"
                    value={formData.sample_id}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">اختر العينة</option>
                    {operations.samples.filter(s => s.status === 'مكتمل').map(sample => (
                      <option key={sample.sample_id} value={sample.sample_id}>
                        عينة #{sample.sample_id} - {sample.customer_name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="form-col">
                <div className="form-group">
                  <label>الوزن الخام (غرام) *</label>
                  <input
                    type="number"
                    name="raw_weight"
                    value={formData.raw_weight}
                    onChange={handleInputChange}
                    required
                    step="0.1"
                    placeholder="أدخل الوزن الخام"
                  />
                </div>
              </div>
            </div>
            
            <div className="form-row">
              <div className="form-col">
                <div className="form-group">
                  <label>المواد الكيميائية المستخدمة</label>
                  <input
                    type="text"
                    name="chemicals_used"
                    value={formData.chemicals_used}
                    onChange={handleInputChange}
                    placeholder="أدخل المواد المستخدمة"
                  />
                </div>
              </div>
              
              <div className="form-col">
                <div className="form-group">
                  <label>فني التنقية</label>
                  <select
                    name="employee_id"
                    value={formData.employee_id}
                    onChange={handleInputChange}
                  >
                    <option value="">اختر فني التنقية</option>
                    {employees.filter(e => e.role === 'فني تنقية').map(employee => (
                      <option key={employee.employee_id} value={employee.employee_id}>
                        {employee.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </>
        );

      case 'castings':
        return (
          <>
            <div className="form-row">
              <div className="form-col">
                <div className="form-group">
                  <label>العميل *</label>
                  <select
                    name="customer_id"
                    value={formData.customer_id}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">اختر العميل</option>
                    {customers.map(customer => (
                      <option key={customer.customer_id} value={customer.customer_id}>
                        {customer.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="form-col">
                <div className="form-group">
                  <label>وزن السبيكة (غرام) *</label>
                  <input
                    type="number"
                    name="bar_weight"
                    value={formData.bar_weight}
                    onChange={handleInputChange}
                    required
                    step="0.1"
                    placeholder="أدخل وزن السبيكة"
                  />
                </div>
              </div>
            </div>
            
            <div className="form-row">
              <div className="form-col">
                <div className="form-group">
                  <label>شكل السبيكة *</label>
                  <select
                    name="bar_shape"
                    value={formData.bar_shape}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">اختر الشكل</option>
                    {barShapes.map(shape => (
                      <option key={shape} value={shape}>{shape}</option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="form-col">
                <div className="form-group">
                  <label>نسبة النقاء (%) *</label>
                  <input
                    type="number"
                    name="purity_percentage"
                    value={formData.purity_percentage}
                    onChange={handleInputChange}
                    required
                    step="0.1"
                    min="0"
                    max="100"
                    placeholder="أدخل نسبة النقاء"
                  />
                </div>
              </div>
            </div>
            
            <div className="form-row">
              <div className="form-col">
                <div className="form-group">
                  <label>فني الصب</label>
                  <select
                    name="employee_id"
                    value={formData.employee_id}
                    onChange={handleInputChange}
                  >
                    <option value="">اختر فني الصب</option>
                    {employees.filter(e => e.role === 'فني صب').map(employee => (
                      <option key={employee.employee_id} value={employee.employee_id}>
                        {employee.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </>
        );

      default:
        return null;
    }
  };

  const renderOperationsTable = () => {
    const currentOperations = operations[activeTab];
    
    if (currentOperations.length === 0) {
      return (
        <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
          لا توجد عمليات مسجلة
        </div>
      );
    }

    return (
      <div className="table-container">
        <table className="table">
          <thead>
            <tr>
              {activeTab === 'samples' && (
                <>
                  <th>رقم العينة</th>
                  <th>العميل</th>
                  <th>الوزن</th>
                  <th>نوع المادة</th>
                  <th>نسبة النقاء</th>
                  <th>التكلفة</th>
                  <th>المحلل</th>
                  <th>التاريخ</th>
                  <th>الحالة</th>
                </>
              )}
              
              {activeTab === 'refinements' && (
                <>
                  <th>رقم التنقية</th>
                  <th>العميل</th>
                  <th>الوزن الخام</th>
                  <th>الوزن المنقى</th>
                  <th>نسبة الاسترداد</th>
                  <th>المواد المستخدمة</th>
                  <th>الفني</th>
                  <th>التاريخ</th>
                  <th>الحالة</th>
                </>
              )}
              
              {activeTab === 'castings' && (
                <>
                  <th>رقم الصب</th>
                  <th>العميل</th>
                  <th>وزن السبيكة</th>
                  <th>الشكل</th>
                  <th>نسبة النقاء</th>
                  <th>الرقم التسلسلي</th>
                  <th>الفني</th>
                  <th>التاريخ</th>
                  <th>الحالة</th>
                </>
              )}
            </tr>
          </thead>
          <tbody>
            {currentOperations.map((operation) => (
              <tr key={operation[`${activeTab.slice(0, -1)}_id`]}>
                {activeTab === 'samples' && (
                  <>
                    <td>#{operation.sample_id}</td>
                    <td>{operation.customer_name}</td>
                    <td>{operation.weight} غ</td>
                    <td>{operation.material_type}</td>
                    <td>{operation.purity_percentage ? `${operation.purity_percentage}%` : '-'}</td>
                    <td>{operation.analysis_cost} ر.س</td>
                    <td>{operation.employee_name || '-'}</td>
                    <td>{operation.analysis_date}</td>
                    <td>
                      <span style={{
                        padding: '4px 8px',
                        borderRadius: '4px',
                        backgroundColor: getStatusColor(operation.status),
                        color: 'white',
                        fontSize: '12px'
                      }}>
                        {operation.status}
                      </span>
                    </td>
                  </>
                )}
                
                {activeTab === 'refinements' && (
                  <>
                    <td>#{operation.refinement_id}</td>
                    <td>{operation.customer_name}</td>
                    <td>{operation.raw_weight} غ</td>
                    <td>{operation.refined_weight ? `${operation.refined_weight} غ` : '-'}</td>
                    <td>{operation.recovery_percentage ? `${operation.recovery_percentage}%` : '-'}</td>
                    <td>{operation.chemicals_used || '-'}</td>
                    <td>{operation.employee_name || '-'}</td>
                    <td>{operation.refinement_date}</td>
                    <td>
                      <span style={{
                        padding: '4px 8px',
                        borderRadius: '4px',
                        backgroundColor: getStatusColor(operation.status),
                        color: 'white',
                        fontSize: '12px'
                      }}>
                        {operation.status}
                      </span>
                    </td>
                  </>
                )}
                
                {activeTab === 'castings' && (
                  <>
                    <td>#{operation.casting_id}</td>
                    <td>{operation.customer_name}</td>
                    <td>{operation.bar_weight} غ</td>
                    <td>{operation.bar_shape}</td>
                    <td>{operation.purity_percentage}%</td>
                    <td>{operation.serial_number}</td>
                    <td>{operation.employee_name || '-'}</td>
                    <td>{operation.casting_date}</td>
                    <td>
                      <span style={{
                        padding: '4px 8px',
                        borderRadius: '4px',
                        backgroundColor: getStatusColor(operation.status),
                        color: 'white',
                        fontSize: '12px'
                      }}>
                        {operation.status}
                      </span>
                    </td>
                  </>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  // إخفاء الرسالة بعد 3 ثوان
  useEffect(() => {
    if (message.text) {
      const timer = setTimeout(() => {
        setMessage({ type: '', text: '' });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  if (isLoading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="operations-management">
      <div className="card">
        <div className="card-header">
          <h1 className="card-title">إدارة العمليات</h1>
          <button 
            className="btn btn-primary"
            onClick={handleAddNew}
          >
            ➕ إضافة عملية جديدة
          </button>
        </div>

        {message.text && (
          <div className={`alert alert-${message.type === 'success' ? 'success' : 'error'}`}>
            {message.text}
          </div>
        )}

        {/* التبويبات */}
        <div className="tabs" style={{ marginBottom: '20px' }}>
          <div style={{ display: 'flex', borderBottom: '1px solid #e0e0e0' }}>
            {tabs.map((tab) => (
              <button
                key={tab.id}
                className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
                onClick={() => handleTabChange(tab.id)}
                style={{
                  padding: '15px 20px',
                  border: 'none',
                  background: activeTab === tab.id ? '#667eea' : 'transparent',
                  color: activeTab === tab.id ? 'white' : '#666',
                  cursor: 'pointer',
                  borderRadius: '8px 8px 0 0',
                  marginLeft: '5px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                <span>{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* نموذج إضافة عملية جديدة */}
        {showAddForm && (
          <div className="card" style={{ marginBottom: '20px' }}>
            <div className="card-header">
              <h3>إضافة {tabs.find(t => t.id === activeTab)?.label}</h3>
              <button 
                className="btn btn-secondary"
                onClick={() => setShowAddForm(false)}
              >
                ✕ إلغاء
              </button>
            </div>
            
            <form onSubmit={handleSubmit}>
              {renderFormFields()}
              
              <div className="form-actions">
                <button type="submit" className="btn btn-success">
                  ➕ إضافة العملية
                </button>
                <button 
                  type="button" 
                  className="btn btn-secondary" 
                  onClick={() => setShowAddForm(false)}
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        )}

        {/* جدول العمليات */}
        {renderOperationsTable()}
      </div>
    </div>
  );
};

export default OperationsManagement;

