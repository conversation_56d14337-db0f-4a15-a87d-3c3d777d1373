import React, { useState, useEffect } from 'react';

const CustomerManagement = () => {
  const [customers, setCustomers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    id_number: '',
    phone: '',
    address: ''
  });
  const [message, setMessage] = useState({ type: '', text: '' });

  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = async () => {
    setIsLoading(true);
    
    // محاكاة استدعاء API
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // بيانات تجريبية
    const mockCustomers = [
      {
        customer_id: 1,
        name: '<PERSON><PERSON><PERSON><PERSON> محمد علي',
        id_number: '1234567890',
        phone: '0501234567',
        address: 'الرياض، حي النخيل',
        created_at: '2025-01-01',
        total_operations: 15,
        total_amount: 12500
      },
      {
        customer_id: 2,
        name: 'فاطمة أحمد السالم',
        id_number: '0987654321',
        phone: '0509876543',
        address: 'جدة، حي الصفا',
        created_at: '2025-01-15',
        total_operations: 8,
        total_amount: 6750
      },
      {
        customer_id: 3,
        name: 'محمد عبدالله الأحمد',
        id_number: '1122334455',
        phone: '0551122334',
        address: 'الدمام، حي الفيصلية',
        created_at: '2025-02-01',
        total_operations: 22,
        total_amount: 18900
      }
    ];
    
    setCustomers(mockCustomers);
    setIsLoading(false);
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      // محاكاة استدعاء API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      if (editingCustomer) {
        // تحديث عميل موجود
        setCustomers(customers.map(customer => 
          customer.customer_id === editingCustomer.customer_id 
            ? { ...customer, ...formData }
            : customer
        ));
        setMessage({ type: 'success', text: 'تم تحديث بيانات العميل بنجاح' });
      } else {
        // إضافة عميل جديد
        const newCustomer = {
          customer_id: customers.length + 1,
          ...formData,
          created_at: new Date().toISOString().split('T')[0],
          total_operations: 0,
          total_amount: 0
        };
        setCustomers([...customers, newCustomer]);
        setMessage({ type: 'success', text: 'تم إضافة العميل بنجاح' });
      }
      
      resetForm();
    } catch (error) {
      setMessage({ type: 'error', text: 'حدث خطأ أثناء حفظ البيانات' });
    }
  };

  const handleEdit = (customer) => {
    setEditingCustomer(customer);
    setFormData({
      name: customer.name,
      id_number: customer.id_number,
      phone: customer.phone,
      address: customer.address
    });
    setShowAddForm(true);
  };

  const handleDelete = async (customerId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      try {
        // محاكاة استدعاء API
        await new Promise(resolve => setTimeout(resolve, 500));
        
        setCustomers(customers.filter(customer => customer.customer_id !== customerId));
        setMessage({ type: 'success', text: 'تم حذف العميل بنجاح' });
      } catch (error) {
        setMessage({ type: 'error', text: 'حدث خطأ أثناء حذف العميل' });
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      id_number: '',
      phone: '',
      address: ''
    });
    setEditingCustomer(null);
    setShowAddForm(false);
  };

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.id_number.includes(searchTerm) ||
    customer.phone.includes(searchTerm)
  );

  // إخفاء الرسالة بعد 3 ثوان
  useEffect(() => {
    if (message.text) {
      const timer = setTimeout(() => {
        setMessage({ type: '', text: '' });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  if (isLoading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="customer-management">
      <div className="card">
        <div className="card-header">
          <h1 className="card-title">إدارة العملاء</h1>
          <button 
            className="btn btn-primary"
            onClick={() => setShowAddForm(true)}
          >
            ➕ إضافة عميل جديد
          </button>
        </div>

        {message.text && (
          <div className={`alert alert-${message.type === 'success' ? 'success' : 'error'}`}>
            {message.text}
          </div>
        )}

        {/* نموذج إضافة/تعديل العميل */}
        {showAddForm && (
          <div className="card" style={{ marginBottom: '20px' }}>
            <div className="card-header">
              <h3>{editingCustomer ? 'تعديل بيانات العميل' : 'إضافة عميل جديد'}</h3>
              <button 
                className="btn btn-secondary"
                onClick={resetForm}
              >
                ✕ إلغاء
              </button>
            </div>
            
            <form onSubmit={handleSubmit}>
              <div className="form-row">
                <div className="form-col">
                  <div className="form-group">
                    <label>اسم العميل *</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      placeholder="أدخل اسم العميل"
                    />
                  </div>
                </div>
                
                <div className="form-col">
                  <div className="form-group">
                    <label>رقم الهوية *</label>
                    <input
                      type="text"
                      name="id_number"
                      value={formData.id_number}
                      onChange={handleInputChange}
                      required
                      placeholder="أدخل رقم الهوية"
                    />
                  </div>
                </div>
              </div>
              
              <div className="form-row">
                <div className="form-col">
                  <div className="form-group">
                    <label>رقم الهاتف</label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="أدخل رقم الهاتف"
                    />
                  </div>
                </div>
                
                <div className="form-col">
                  <div className="form-group">
                    <label>العنوان</label>
                    <input
                      type="text"
                      name="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      placeholder="أدخل العنوان"
                    />
                  </div>
                </div>
              </div>
              
              <div className="form-actions">
                <button type="submit" className="btn btn-success">
                  {editingCustomer ? '💾 حفظ التغييرات' : '➕ إضافة العميل'}
                </button>
                <button type="button" className="btn btn-secondary" onClick={resetForm}>
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        )}

        {/* البحث */}
        <div className="form-group" style={{ marginBottom: '20px' }}>
          <input
            type="text"
            placeholder="البحث بالاسم أو رقم الهوية أو الهاتف..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{ width: '100%', maxWidth: '400px' }}
          />
        </div>

        {/* جدول العملاء */}
        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>الاسم</th>
                <th>رقم الهوية</th>
                <th>الهاتف</th>
                <th>العنوان</th>
                <th>عدد العمليات</th>
                <th>إجمالي المبلغ</th>
                <th>تاريخ التسجيل</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredCustomers.map((customer) => (
                <tr key={customer.customer_id}>
                  <td>{customer.name}</td>
                  <td>{customer.id_number}</td>
                  <td>{customer.phone || '-'}</td>
                  <td>{customer.address || '-'}</td>
                  <td>{customer.total_operations}</td>
                  <td>{customer.total_amount.toLocaleString()} ر.س</td>
                  <td>{customer.created_at}</td>
                  <td>
                    <div style={{ display: 'flex', gap: '5px' }}>
                      <button
                        className="btn btn-warning"
                        onClick={() => handleEdit(customer)}
                        style={{ padding: '5px 10px', fontSize: '12px' }}
                      >
                        ✏️ تعديل
                      </button>
                      <button
                        className="btn btn-danger"
                        onClick={() => handleDelete(customer.customer_id)}
                        style={{ padding: '5px 10px', fontSize: '12px' }}
                      >
                        🗑️ حذف
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredCustomers.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
            {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد عملاء مسجلين'}
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomerManagement;

