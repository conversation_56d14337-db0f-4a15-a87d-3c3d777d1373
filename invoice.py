"""
نموذج الفواتير - يحتوي على جميع العمليات المتعلقة بإدارة الفواتير والمدفوعات
"""

from typing import List, Dict, Optional
from datetime import datetime
from database_manager import DatabaseManager


class Invoice:
    """نموذج الفواتير"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        تهيئة نموذج الفواتير
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db = db_manager
    
    def create_invoice(self, customer_id: int, total_amount: float, 
                      sample_id: int = None, refinement_id: int = None, 
                      casting_id: int = None, paid_amount: float = 0) -> int:
        """
        إنشاء فاتورة جديدة
        
        Args:
            customer_id: معرف العميل
            total_amount: المبلغ الإجمالي
            sample_id: معرف العينة (اختياري)
            refinement_id: معرف عملية التنقية (اختياري)
            casting_id: معرف عملية الصب (اختياري)
            paid_amount: المبلغ المدفوع (افتراضي 0)
            
        Returns:
            معرف الفاتورة الجديدة
        """
        # تحديد حالة الفاتورة
        status = "مدفوع" if paid_amount >= total_amount else "غير مدفوع"
        
        query = """
            INSERT INTO Invoices (customer_id, sample_id, refinement_id, casting_id, 
                                total_amount, paid_amount, status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        return self.db.execute_update(query, (customer_id, sample_id, refinement_id, 
                                            casting_id, total_amount, paid_amount, status))
    
    def create_invoice_for_sample(self, sample_id: int, paid_amount: float = 0) -> int:
        """
        إنشاء فاتورة لعينة تحليل
        
        Args:
            sample_id: معرف العينة
            paid_amount: المبلغ المدفوع (افتراضي 0)
            
        Returns:
            معرف الفاتورة الجديدة
        """
        # الحصول على بيانات العينة
        sample_query = "SELECT customer_id, analysis_cost FROM Samples WHERE sample_id = ?"
        sample_result = self.db.execute_query(sample_query, (sample_id,))
        
        if not sample_result:
            raise ValueError(f"العينة رقم {sample_id} غير موجودة")
        
        sample = dict(sample_result[0])
        return self.create_invoice(
            customer_id=sample['customer_id'],
            total_amount=sample['analysis_cost'] or 0,
            sample_id=sample_id,
            paid_amount=paid_amount
        )
    
    def create_invoice_for_casting(self, casting_id: int, casting_cost: float, paid_amount: float = 0) -> int:
        """
        إنشاء فاتورة لعملية صب
        
        Args:
            casting_id: معرف عملية الصب
            casting_cost: تكلفة الصب
            paid_amount: المبلغ المدفوع (افتراضي 0)
            
        Returns:
            معرف الفاتورة الجديدة
        """
        # الحصول على بيانات عملية الصب
        casting_query = "SELECT customer_id FROM Castings WHERE casting_id = ?"
        casting_result = self.db.execute_query(casting_query, (casting_id,))
        
        if not casting_result:
            raise ValueError(f"عملية الصب رقم {casting_id} غير موجودة")
        
        casting = dict(casting_result[0])
        return self.create_invoice(
            customer_id=casting['customer_id'],
            total_amount=casting_cost,
            casting_id=casting_id,
            paid_amount=paid_amount
        )
    
    def get_invoice_by_id(self, invoice_id: int) -> Optional[Dict]:
        """
        الحصول على فاتورة بواسطة المعرف
        
        Args:
            invoice_id: معرف الفاتورة
            
        Returns:
            بيانات الفاتورة مع معلومات العميل والعمليات المرتبطة
        """
        query = """
            SELECT i.*, c.name as customer_name, c.id_number as customer_id_number,
                   c.phone as customer_phone, c.address as customer_address,
                   s.weight as sample_weight, s.material_type,
                   cast.bar_weight, cast.serial_number
            FROM Invoices i
            LEFT JOIN Customers c ON i.customer_id = c.customer_id
            LEFT JOIN Samples s ON i.sample_id = s.sample_id
            LEFT JOIN Castings cast ON i.casting_id = cast.casting_id
            WHERE i.invoice_id = ?
        """
        results = self.db.execute_query(query, (invoice_id,))
        return dict(results[0]) if results else None
    
    def get_invoices_by_customer(self, customer_id: int) -> List[Dict]:
        """
        الحصول على جميع فواتير عميل معين
        
        Args:
            customer_id: معرف العميل
            
        Returns:
            قائمة بفواتير العميل
        """
        query = """
            SELECT i.*, s.weight as sample_weight, s.material_type,
                   cast.bar_weight, cast.serial_number
            FROM Invoices i
            LEFT JOIN Samples s ON i.sample_id = s.sample_id
            LEFT JOIN Castings cast ON i.casting_id = cast.casting_id
            WHERE i.customer_id = ?
            ORDER BY i.invoice_date DESC
        """
        results = self.db.execute_query(query, (customer_id,))
        return [dict(row) for row in results]
    
    def get_unpaid_invoices(self) -> List[Dict]:
        """
        الحصول على جميع الفواتير غير المدفوعة
        
        Returns:
            قائمة بالفواتير غير المدفوعة
        """
        query = """
            SELECT i.*, c.name as customer_name, c.phone as customer_phone,
                   (i.total_amount - i.paid_amount) as outstanding_amount
            FROM Invoices i
            LEFT JOIN Customers c ON i.customer_id = c.customer_id
            WHERE i.status = 'غير مدفوع'
            ORDER BY i.invoice_date
        """
        results = self.db.execute_query(query)
        return [dict(row) for row in results]
    
    def add_payment(self, invoice_id: int, payment_amount: float) -> bool:
        """
        إضافة دفعة لفاتورة
        
        Args:
            invoice_id: معرف الفاتورة
            payment_amount: مبلغ الدفعة
            
        Returns:
            True إذا تم إضافة الدفعة بنجاح
        """
        # الحصول على بيانات الفاتورة الحالية
        invoice = self.get_invoice_by_id(invoice_id)
        if not invoice:
            return False
        
        # حساب المبلغ الجديد المدفوع
        new_paid_amount = invoice['paid_amount'] + payment_amount
        
        # تحديد الحالة الجديدة
        new_status = "مدفوع" if new_paid_amount >= invoice['total_amount'] else "مدفوع جزئياً"
        
        # تحديث الفاتورة
        query = """
            UPDATE Invoices 
            SET paid_amount = ?, status = ?
            WHERE invoice_id = ?
        """
        rows_affected = self.db.execute_update(query, (new_paid_amount, new_status, invoice_id))
        return rows_affected > 0
    
    def update_invoice_status(self, invoice_id: int, status: str) -> bool:
        """
        تحديث حالة الفاتورة
        
        Args:
            invoice_id: معرف الفاتورة
            status: الحالة الجديدة (مدفوع، غير مدفوع، مدفوع جزئياً، ملغى)
            
        Returns:
            True إذا تم التحديث بنجاح
        """
        valid_statuses = ['مدفوع', 'غير مدفوع', 'مدفوع جزئياً', 'ملغى']
        if status not in valid_statuses:
            raise ValueError(f"الحالة يجب أن تكون إحدى: {', '.join(valid_statuses)}")
        
        query = "UPDATE Invoices SET status = ? WHERE invoice_id = ?"
        rows_affected = self.db.execute_update(query, (status, invoice_id))
        return rows_affected > 0
    
    def delete_invoice(self, invoice_id: int) -> bool:
        """
        حذف فاتورة
        
        Args:
            invoice_id: معرف الفاتورة
            
        Returns:
            True إذا تم الحذف بنجاح
        """
        query = "DELETE FROM Invoices WHERE invoice_id = ?"
        rows_affected = self.db.execute_update(query, (invoice_id,))
        return rows_affected > 0
    
    def get_financial_summary(self, start_date: str = None, end_date: str = None) -> Dict:
        """
        الحصول على ملخص مالي لفترة معينة
        
        Args:
            start_date: تاريخ البداية (YYYY-MM-DD)
            end_date: تاريخ النهاية (YYYY-MM-DD)
            
        Returns:
            ملخص مالي
        """
        date_filter = ""
        params = []
        
        if start_date and end_date:
            date_filter = " WHERE DATE(invoice_date) BETWEEN ? AND ?"
            params = [start_date, end_date]
        
        query = f"""
            SELECT 
                COUNT(*) as total_invoices,
                SUM(total_amount) as total_amount,
                SUM(paid_amount) as total_paid,
                SUM(total_amount - paid_amount) as total_outstanding,
                COUNT(CASE WHEN status = 'مدفوع' THEN 1 END) as paid_invoices,
                COUNT(CASE WHEN status = 'غير مدفوع' THEN 1 END) as unpaid_invoices
            FROM Invoices{date_filter}
        """
        
        result = self.db.execute_query(query, params)
        
        if result:
            summary = dict(result[0])
            # تعامل مع القيم الفارغة
            for key in ['total_amount', 'total_paid', 'total_outstanding']:
                if summary[key] is None:
                    summary[key] = 0.0
            return summary
        
        return {
            'total_invoices': 0,
            'total_amount': 0.0,
            'total_paid': 0.0,
            'total_outstanding': 0.0,
            'paid_invoices': 0,
            'unpaid_invoices': 0
        }
    
    def get_customer_outstanding_balance(self, customer_id: int) -> float:
        """
        الحصول على الرصيد المستحق لعميل معين
        
        Args:
            customer_id: معرف العميل
            
        Returns:
            المبلغ المستحق
        """
        query = """
            SELECT SUM(total_amount - paid_amount) as outstanding
            FROM Invoices
            WHERE customer_id = ? AND status != 'مدفوع'
        """
        result = self.db.execute_query(query, (customer_id,))
        return result[0]['outstanding'] or 0.0 if result else 0.0
    
    def generate_invoice_number(self, invoice_id: int) -> str:
        """
        توليد رقم فاتورة
        
        Args:
            invoice_id: معرف الفاتورة
            
        Returns:
            رقم الفاتورة
        """
        current_year = datetime.now().year
        return f"INV-{current_year}-{invoice_id:06d}"

