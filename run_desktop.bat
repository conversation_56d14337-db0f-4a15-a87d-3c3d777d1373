@echo off
echo.
echo ========================================
echo Gold Lab Management System - Desktop
echo ========================================
echo.

echo Starting Desktop Application...

:: Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not installed. Please install Python 3.8 or newer
    pause
    exit /b 1
)

:: Run the desktop application
python run_desktop.py

pause
