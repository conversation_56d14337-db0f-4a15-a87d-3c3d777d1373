import React, { useState, useEffect } from 'react';
import './App.css';

// مكونات الواجهة
import LoginForm from './components/LoginForm';
import Dashboard from './components/Dashboard';
import CustomerManagement from './components/CustomerManagement';
import EmployeeManagement from './components/EmployeeManagement';
import OperationsManagement from './components/OperationsManagement';
import InvoiceManagement from './components/InvoiceManagement';
import Reports from './components/Reports';
import Sidebar from './components/Sidebar';
import Header from './components/Header';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [activeSection, setActiveSection] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // التحقق من حالة تسجيل الدخول عند تحميل التطبيق
  useEffect(() => {
    const savedAuth = localStorage.getItem('isAuthenticated');
    const savedUser = localStorage.getItem('currentUser');
    
    if (savedAuth === 'true' && savedUser) {
      setIsAuthenticated(true);
      setCurrentUser(JSON.parse(savedUser));
    }
  }, []);

  // تسجيل الدخول
  const handleLogin = (userData) => {
    setIsAuthenticated(true);
    setCurrentUser(userData);
    localStorage.setItem('isAuthenticated', 'true');
    localStorage.setItem('currentUser', JSON.stringify(userData));
  };

  // تسجيل الخروج
  const handleLogout = () => {
    setIsAuthenticated(false);
    setCurrentUser(null);
    setActiveSection('dashboard');
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('currentUser');
  };

  // تبديل الشريط الجانبي
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // عرض المكون المناسب حسب القسم النشط
  const renderActiveSection = () => {
    switch (activeSection) {
      case 'dashboard':
        return <Dashboard />;
      case 'customers':
        return <CustomerManagement />;
      case 'employees':
        return <EmployeeManagement />;
      case 'operations':
        return <OperationsManagement />;
      case 'invoices':
        return <InvoiceManagement />;
      case 'reports':
        return <Reports />;
      default:
        return <Dashboard />;
    }
  };

  // إذا لم يتم تسجيل الدخول، عرض نموذج تسجيل الدخول
  if (!isAuthenticated) {
    return (
      <div className="app">
        <LoginForm onLogin={handleLogin} />
      </div>
    );
  }

  // عرض التطبيق الرئيسي بعد تسجيل الدخول
  return (
    <div className="app">
      <div className={`app-layout ${sidebarOpen ? 'sidebar-open' : 'sidebar-closed'}`}>
        {/* الشريط الجانبي */}
        <Sidebar
          isOpen={sidebarOpen}
          activeSection={activeSection}
          onSectionChange={setActiveSection}
        />
        
        {/* المحتوى الرئيسي */}
        <div className="main-content">
          {/* الرأس */}
          <Header
            currentUser={currentUser}
            onToggleSidebar={toggleSidebar}
            onLogout={handleLogout}
          />
          
          {/* المحتوى */}
          <main className="content">
            {renderActiveSection()}
          </main>
        </div>
      </div>
    </div>
  );
}

export default App;

