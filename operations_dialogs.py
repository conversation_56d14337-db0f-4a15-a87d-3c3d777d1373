#!/usr/bin/env python3
"""
حوارات إدارة العمليات والفواتير
Operations and Invoices Dialogs
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import font as tkFont
from datetime import datetime

class SampleAnalysisDialog:
    """حوار إضافة عينة للتحليل"""
    
    def __init__(self, parent, title, customers_list, employees_list, sample_data=None):
        self.result = None
        self.sample_data = sample_data
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد الخطوط
        self.setup_fonts()
        
        # حفظ القوائم
        self.customers_list = customers_list
        self.employees_list = employees_list
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تعبئة البيانات إذا كانت متوفرة
        if sample_data:
            self.populate_data()
        
        # انتظار إغلاق النافذة
        self.dialog.wait_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_fonts(self):
        """إعداد الخطوط"""
        self.fonts = {
            'title': tkFont.Font(family="Arial", size=14, weight="bold"),
            'normal': tkFont.Font(family="Arial", size=11),
            'button': tkFont.Font(family="Arial", size=10, weight="bold")
        }
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.dialog, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_text = "تحديث العينة" if self.sample_data else "إضافة عينة للتحليل"
        title_label = tk.Label(main_frame, text=title_text, font=self.fonts['title'])
        title_label.pack(pady=(0, 20))
        
        # اختيار العميل
        tk.Label(main_frame, text="العميل *:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        self.customer_combobox = ttk.Combobox(main_frame, font=self.fonts['normal'], 
                                             values=[f"{c['customer_id']} - {c['name']}" for c in self.customers_list],
                                             state="readonly", width=40)
        self.customer_combobox.pack(fill=tk.X, pady=(0, 15))
        
        # وزن العينة
        tk.Label(main_frame, text="وزن العينة (جرام) *:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        self.weight_entry = tk.Entry(main_frame, font=self.fonts['normal'], width=40)
        self.weight_entry.pack(fill=tk.X, pady=(0, 15))
        
        # نوع المادة
        tk.Label(main_frame, text="نوع المادة *:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        self.material_combobox = ttk.Combobox(main_frame, font=self.fonts['normal'],
                                             values=["ذهب", "فضة", "بلاتين", "نحاس", "مختلط", "أخرى"],
                                             width=37)
        self.material_combobox.pack(fill=tk.X, pady=(0, 15))
        
        # اختيار الموظف (اختياري)
        tk.Label(main_frame, text="الموظف المسؤول:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        analyst_employees = [emp for emp in self.employees_list if emp.get('role') == 'محلل']
        self.employee_combobox = ttk.Combobox(main_frame, font=self.fonts['normal'],
                                             values=[""] + [f"{e['employee_id']} - {e['name']}" for e in analyst_employees],
                                             state="readonly", width=37)
        self.employee_combobox.pack(fill=tk.X, pady=(0, 15))
        
        # إذا كانت العينة موجودة، أضف حقول النتائج
        if self.sample_data:
            self.create_results_fields(main_frame)
        
        # ملاحظة الحقول المطلوبة
        note_label = tk.Label(main_frame, text="* حقول مطلوبة", font=self.fonts['normal'], fg="red")
        note_label.pack(anchor=tk.W, pady=(0, 15))
        
        # أزرار العمليات
        self.create_buttons(main_frame)
        
        # ربط Enter بالحفظ
        self.dialog.bind('<Return>', lambda e: self.save_sample())
        self.dialog.bind('<Escape>', lambda e: self.cancel())
    
    def create_results_fields(self, parent):
        """إنشاء حقول النتائج للتحديث"""
        # نسبة النقاء
        tk.Label(parent, text="نسبة النقاء (%) *:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        self.purity_entry = tk.Entry(parent, font=self.fonts['normal'], width=40)
        self.purity_entry.pack(fill=tk.X, pady=(0, 15))
        
        # الشوائب
        tk.Label(parent, text="الشوائب:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        self.impurities_text = tk.Text(parent, font=self.fonts['normal'], width=40, height=3)
        self.impurities_text.pack(fill=tk.X, pady=(0, 15))
    
    def create_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = tk.Frame(parent)
        buttons_frame.pack(fill=tk.X)
        
        # زر الحفظ
        save_text = "تحديث النتائج" if self.sample_data else "حفظ العينة"
        save_btn = tk.Button(buttons_frame, text=save_text, font=self.fonts['button'],
                            bg="#28A745", fg="white", command=self.save_sample, width=15)
        save_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="إلغاء", font=self.fonts['button'],
                              bg="#6C757D", fg="white", command=self.cancel, width=12)
        cancel_btn.pack(side=tk.RIGHT)
    
    def populate_data(self):
        """تعبئة البيانات للتحديث"""
        if not self.sample_data:
            return
        
        # تعبئة بيانات العينة الأساسية
        customer_text = f"{self.sample_data.get('customer_id', '')} - {self.sample_data.get('customer_name', '')}"
        self.customer_combobox.set(customer_text)
        self.weight_entry.insert(0, str(self.sample_data.get('weight', '')))
        self.material_combobox.set(self.sample_data.get('material_type', ''))
        
        if self.sample_data.get('employee_id'):
            employee_text = f"{self.sample_data.get('employee_id', '')} - {self.sample_data.get('employee_name', '')}"
            self.employee_combobox.set(employee_text)
        
        # تعبئة النتائج إذا كانت متوفرة
        if hasattr(self, 'purity_entry'):
            self.purity_entry.insert(0, str(self.sample_data.get('purity_percentage', '')))
            self.impurities_text.insert('1.0', self.sample_data.get('impurities', ''))
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.customer_combobox.get():
            messagebox.showerror("خطأ", "يرجى اختيار العميل")
            return False
        
        try:
            weight = float(self.weight_entry.get())
            if weight <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال وزن صحيح للعينة")
            self.weight_entry.focus()
            return False
        
        if not self.material_combobox.get():
            messagebox.showerror("خطأ", "يرجى اختيار نوع المادة")
            return False
        
        # التحقق من النتائج إذا كانت العينة للتحديث
        if self.sample_data and hasattr(self, 'purity_entry'):
            try:
                purity = float(self.purity_entry.get())
                if not (0 <= purity <= 100):
                    raise ValueError()
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال نسبة نقاء صحيحة (0-100)")
                self.purity_entry.focus()
                return False
        
        return True
    
    def save_sample(self):
        """حفظ بيانات العينة"""
        if not self.validate_data():
            return
        
        # استخراج معرف العميل
        customer_text = self.customer_combobox.get()
        customer_id = int(customer_text.split(' - ')[0])
        
        # استخراج معرف الموظف إذا تم اختياره
        employee_id = None
        if self.employee_combobox.get():
            employee_text = self.employee_combobox.get()
            employee_id = int(employee_text.split(' - ')[0])
        
        # جمع البيانات
        self.result = {
            'customer_id': customer_id,
            'weight': float(self.weight_entry.get()),
            'material_type': self.material_combobox.get(),
            'employee_id': employee_id
        }
        
        # إضافة النتائج إذا كانت العينة للتحديث
        if self.sample_data and hasattr(self, 'purity_entry'):
            self.result.update({
                'sample_id': self.sample_data.get('sample_id'),
                'purity_percentage': float(self.purity_entry.get()),
                'impurities': self.impurities_text.get('1.0', tk.END).strip() or None
            })
        
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()


class RefinementDialog:
    """حوار إضافة/تحديث عملية تنقية"""
    
    def __init__(self, parent, title, samples_list, employees_list, refinement_data=None):
        self.result = None
        self.refinement_data = refinement_data
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x450")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد الخطوط
        self.setup_fonts()
        
        # حفظ القوائم
        self.samples_list = samples_list
        self.employees_list = employees_list
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تعبئة البيانات إذا كانت متوفرة
        if refinement_data:
            self.populate_data()
        
        # انتظار إغلاق النافذة
        self.dialog.wait_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_fonts(self):
        """إعداد الخطوط"""
        self.fonts = {
            'title': tkFont.Font(family="Arial", size=14, weight="bold"),
            'normal': tkFont.Font(family="Arial", size=11),
            'button': tkFont.Font(family="Arial", size=10, weight="bold")
        }
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.dialog, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_text = "تحديث عملية التنقية" if self.refinement_data else "إضافة عملية تنقية"
        title_label = tk.Label(main_frame, text=title_text, font=self.fonts['title'])
        title_label.pack(pady=(0, 20))
        
        # اختيار العينة
        tk.Label(main_frame, text="العينة *:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        self.sample_combobox = ttk.Combobox(main_frame, font=self.fonts['normal'],
                                           values=[f"{s['sample_id']} - {s['customer_name']} ({s['weight']}g)" 
                                                  for s in self.samples_list],
                                           state="readonly", width=40)
        self.sample_combobox.pack(fill=tk.X, pady=(0, 15))
        
        # الوزن الخام
        tk.Label(main_frame, text="الوزن الخام (جرام) *:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        self.raw_weight_entry = tk.Entry(main_frame, font=self.fonts['normal'], width=40)
        self.raw_weight_entry.pack(fill=tk.X, pady=(0, 15))
        
        # المواد الكيميائية المستخدمة
        tk.Label(main_frame, text="المواد الكيميائية المستخدمة:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        self.chemicals_text = tk.Text(main_frame, font=self.fonts['normal'], width=40, height=3)
        self.chemicals_text.pack(fill=tk.X, pady=(0, 15))
        
        # اختيار الموظف
        tk.Label(main_frame, text="فني التنقية:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        refinement_employees = [emp for emp in self.employees_list if emp.get('role') == 'فني تنقية']
        self.employee_combobox = ttk.Combobox(main_frame, font=self.fonts['normal'],
                                             values=[""] + [f"{e['employee_id']} - {e['name']}" for e in refinement_employees],
                                             state="readonly", width=37)
        self.employee_combobox.pack(fill=tk.X, pady=(0, 15))
        
        # إذا كانت العملية موجودة، أضف حقل الوزن المنقى
        if self.refinement_data:
            tk.Label(main_frame, text="الوزن المنقى (جرام) *:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
            self.refined_weight_entry = tk.Entry(main_frame, font=self.fonts['normal'], width=40)
            self.refined_weight_entry.pack(fill=tk.X, pady=(0, 15))
        
        # ملاحظة الحقول المطلوبة
        note_label = tk.Label(main_frame, text="* حقول مطلوبة", font=self.fonts['normal'], fg="red")
        note_label.pack(anchor=tk.W, pady=(0, 15))
        
        # أزرار العمليات
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        # زر الحفظ
        save_text = "تحديث النتائج" if self.refinement_data else "حفظ العملية"
        save_btn = tk.Button(buttons_frame, text=save_text, font=self.fonts['button'],
                            bg="#28A745", fg="white", command=self.save_refinement, width=15)
        save_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="إلغاء", font=self.fonts['button'],
                              bg="#6C757D", fg="white", command=self.cancel, width=12)
        cancel_btn.pack(side=tk.RIGHT)
        
        # ربط Enter بالحفظ
        self.dialog.bind('<Return>', lambda e: self.save_refinement())
        self.dialog.bind('<Escape>', lambda e: self.cancel())
    
    def populate_data(self):
        """تعبئة البيانات للتحديث"""
        if not self.refinement_data:
            return
        
        # تعبئة بيانات العملية
        sample_text = f"{self.refinement_data.get('sample_id', '')} - {self.refinement_data.get('customer_name', '')} ({self.refinement_data.get('sample_weight', '')}g)"
        self.sample_combobox.set(sample_text)
        self.raw_weight_entry.insert(0, str(self.refinement_data.get('raw_weight', '')))
        self.chemicals_text.insert('1.0', self.refinement_data.get('chemicals_used', ''))
        
        if self.refinement_data.get('employee_id'):
            employee_text = f"{self.refinement_data.get('employee_id', '')} - {self.refinement_data.get('employee_name', '')}"
            self.employee_combobox.set(employee_text)
        
        # تعبئة الوزن المنقى إذا كان متوفراً
        if hasattr(self, 'refined_weight_entry'):
            self.refined_weight_entry.insert(0, str(self.refinement_data.get('refined_weight', '')))
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.sample_combobox.get():
            messagebox.showerror("خطأ", "يرجى اختيار العينة")
            return False
        
        try:
            raw_weight = float(self.raw_weight_entry.get())
            if raw_weight <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال وزن خام صحيح")
            self.raw_weight_entry.focus()
            return False
        
        # التحقق من الوزن المنقى إذا كانت العملية للتحديث
        if self.refinement_data and hasattr(self, 'refined_weight_entry'):
            try:
                refined_weight = float(self.refined_weight_entry.get())
                if refined_weight <= 0:
                    raise ValueError()
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال وزن منقى صحيح")
                self.refined_weight_entry.focus()
                return False
        
        return True
    
    def save_refinement(self):
        """حفظ بيانات عملية التنقية"""
        if not self.validate_data():
            return
        
        # استخراج معرف العينة
        sample_text = self.sample_combobox.get()
        sample_id = int(sample_text.split(' - ')[0])
        
        # استخراج معرف الموظف إذا تم اختياره
        employee_id = None
        if self.employee_combobox.get():
            employee_text = self.employee_combobox.get()
            employee_id = int(employee_text.split(' - ')[0])
        
        # جمع البيانات
        self.result = {
            'sample_id': sample_id,
            'raw_weight': float(self.raw_weight_entry.get()),
            'chemicals_used': self.chemicals_text.get('1.0', tk.END).strip() or None,
            'employee_id': employee_id
        }
        
        # إضافة الوزن المنقى إذا كانت العملية للتحديث
        if self.refinement_data and hasattr(self, 'refined_weight_entry'):
            self.result.update({
                'refinement_id': self.refinement_data.get('refinement_id'),
                'refined_weight': float(self.refined_weight_entry.get())
            })
        
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()


class CastingDialog:
    """حوار إضافة عملية صب"""

    def __init__(self, parent, title, customers_list, employees_list, refinements_list=None):
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.center_window()

        # إعداد الخطوط
        self.setup_fonts()

        # حفظ القوائم
        self.customers_list = customers_list
        self.employees_list = employees_list
        self.refinements_list = refinements_list or []

        # إنشاء الواجهة
        self.create_widgets()

        # انتظار إغلاق النافذة
        self.dialog.wait_window()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

    def setup_fonts(self):
        """إعداد الخطوط"""
        self.fonts = {
            'title': tkFont.Font(family="Arial", size=14, weight="bold"),
            'normal': tkFont.Font(family="Arial", size=11),
            'button': tkFont.Font(family="Arial", size=10, weight="bold")
        }

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.dialog, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # العنوان
        title_label = tk.Label(main_frame, text="إضافة عملية صب", font=self.fonts['title'])
        title_label.pack(pady=(0, 20))

        # اختيار العميل
        tk.Label(main_frame, text="العميل *:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        self.customer_combobox = ttk.Combobox(main_frame, font=self.fonts['normal'],
                                             values=[f"{c['customer_id']} - {c['name']}" for c in self.customers_list],
                                             state="readonly", width=40)
        self.customer_combobox.pack(fill=tk.X, pady=(0, 15))

        # وزن السبيكة
        tk.Label(main_frame, text="وزن السبيكة (جرام) *:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        self.bar_weight_entry = tk.Entry(main_frame, font=self.fonts['normal'], width=40)
        self.bar_weight_entry.pack(fill=tk.X, pady=(0, 15))

        # شكل السبيكة
        tk.Label(main_frame, text="شكل السبيكة *:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        self.bar_shape_combobox = ttk.Combobox(main_frame, font=self.fonts['normal'],
                                              values=["سبيكة مستطيلة", "سبيكة دائرية", "قطع صغيرة", "شكل مخصص"],
                                              width=37)
        self.bar_shape_combobox.pack(fill=tk.X, pady=(0, 15))

        # نسبة النقاء
        tk.Label(main_frame, text="نسبة النقاء (%) *:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        self.purity_entry = tk.Entry(main_frame, font=self.fonts['normal'], width=40)
        self.purity_entry.pack(fill=tk.X, pady=(0, 15))

        # عملية التنقية المرتبطة (اختياري)
        tk.Label(main_frame, text="عملية التنقية المرتبطة:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        refinement_values = [""] + [f"{r['refinement_id']} - {r['customer_name']} ({r['refined_weight']}g)"
                                   for r in self.refinements_list]
        self.refinement_combobox = ttk.Combobox(main_frame, font=self.fonts['normal'],
                                               values=refinement_values,
                                               state="readonly", width=37)
        self.refinement_combobox.pack(fill=tk.X, pady=(0, 15))

        # اختيار الموظف
        tk.Label(main_frame, text="فني الصب:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        casting_employees = [emp for emp in self.employees_list if emp.get('role') == 'فني صب']
        self.employee_combobox = ttk.Combobox(main_frame, font=self.fonts['normal'],
                                             values=[""] + [f"{e['employee_id']} - {e['name']}" for e in casting_employees],
                                             state="readonly", width=37)
        self.employee_combobox.pack(fill=tk.X, pady=(0, 15))

        # ملاحظة الحقول المطلوبة
        note_label = tk.Label(main_frame, text="* حقول مطلوبة", font=self.fonts['normal'], fg="red")
        note_label.pack(anchor=tk.W, pady=(0, 15))

        # أزرار العمليات
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        # زر الحفظ
        save_btn = tk.Button(buttons_frame, text="حفظ العملية", font=self.fonts['button'],
                            bg="#28A745", fg="white", command=self.save_casting, width=15)
        save_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="إلغاء", font=self.fonts['button'],
                              bg="#6C757D", fg="white", command=self.cancel, width=12)
        cancel_btn.pack(side=tk.RIGHT)

        # ربط Enter بالحفظ
        self.dialog.bind('<Return>', lambda e: self.save_casting())
        self.dialog.bind('<Escape>', lambda e: self.cancel())

    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.customer_combobox.get():
            messagebox.showerror("خطأ", "يرجى اختيار العميل")
            return False

        try:
            bar_weight = float(self.bar_weight_entry.get())
            if bar_weight <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال وزن صحيح للسبيكة")
            self.bar_weight_entry.focus()
            return False

        if not self.bar_shape_combobox.get():
            messagebox.showerror("خطأ", "يرجى اختيار شكل السبيكة")
            return False

        try:
            purity = float(self.purity_entry.get())
            if not (0 <= purity <= 100):
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال نسبة نقاء صحيحة (0-100)")
            self.purity_entry.focus()
            return False

        return True

    def save_casting(self):
        """حفظ بيانات عملية الصب"""
        if not self.validate_data():
            return

        # استخراج معرف العميل
        customer_text = self.customer_combobox.get()
        customer_id = int(customer_text.split(' - ')[0])

        # استخراج معرف عملية التنقية إذا تم اختيارها
        refinement_id = None
        if self.refinement_combobox.get():
            refinement_text = self.refinement_combobox.get()
            refinement_id = int(refinement_text.split(' - ')[0])

        # استخراج معرف الموظف إذا تم اختياره
        employee_id = None
        if self.employee_combobox.get():
            employee_text = self.employee_combobox.get()
            employee_id = int(employee_text.split(' - ')[0])

        # جمع البيانات
        self.result = {
            'customer_id': customer_id,
            'bar_weight': float(self.bar_weight_entry.get()),
            'bar_shape': self.bar_shape_combobox.get(),
            'purity_percentage': float(self.purity_entry.get()),
            'refinement_id': refinement_id,
            'employee_id': employee_id
        }

        self.dialog.destroy()

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()


class InvoiceDialog:
    """حوار إنشاء/تحديث فاتورة"""

    def __init__(self, parent, title, invoice_type, samples_list=None, castings_list=None, invoice_data=None):
        self.result = None
        self.invoice_type = invoice_type  # 'sample' أو 'casting'
        self.invoice_data = invoice_data

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.center_window()

        # إعداد الخطوط
        self.setup_fonts()

        # حفظ القوائم
        self.samples_list = samples_list or []
        self.castings_list = castings_list or []

        # إنشاء الواجهة
        self.create_widgets()

        # تعبئة البيانات إذا كانت متوفرة
        if invoice_data:
            self.populate_data()

        # انتظار إغلاق النافذة
        self.dialog.wait_window()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

    def setup_fonts(self):
        """إعداد الخطوط"""
        self.fonts = {
            'title': tkFont.Font(family="Arial", size=14, weight="bold"),
            'normal': tkFont.Font(family="Arial", size=11),
            'button': tkFont.Font(family="Arial", size=10, weight="bold")
        }

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.dialog, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # العنوان
        if self.invoice_type == 'sample':
            title_text = "إنشاء فاتورة تحليل عينة"
            item_label = "العينة *:"
            item_values = [f"{s['sample_id']} - {s['customer_name']} ({s['weight']}g)" for s in self.samples_list]
        else:
            title_text = "إنشاء فاتورة عملية صب"
            item_label = "عملية الصب *:"
            item_values = [f"{c['casting_id']} - {c['customer_name']} ({c['bar_weight']}g)" for c in self.castings_list]

        if self.invoice_data:
            title_text = "إضافة دفعة للفاتورة"

        title_label = tk.Label(main_frame, text=title_text, font=self.fonts['title'])
        title_label.pack(pady=(0, 20))

        # اختيار العنصر (عينة أو عملية صب)
        if not self.invoice_data:  # فقط عند إنشاء فاتورة جديدة
            tk.Label(main_frame, text=item_label, font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
            self.item_combobox = ttk.Combobox(main_frame, font=self.fonts['normal'],
                                             values=item_values, state="readonly", width=40)
            self.item_combobox.pack(fill=tk.X, pady=(0, 15))

        # تكلفة الخدمة (فقط لعمليات الصب الجديدة)
        if self.invoice_type == 'casting' and not self.invoice_data:
            tk.Label(main_frame, text="تكلفة الصب *:", font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
            self.casting_cost_entry = tk.Entry(main_frame, font=self.fonts['normal'], width=40)
            self.casting_cost_entry.pack(fill=tk.X, pady=(0, 15))

        # المبلغ المدفوع
        payment_label = "المبلغ المدفوع:" if not self.invoice_data else "مبلغ الدفعة الجديدة *:"
        tk.Label(main_frame, text=payment_label, font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        self.paid_amount_entry = tk.Entry(main_frame, font=self.fonts['normal'], width=40)
        self.paid_amount_entry.pack(fill=tk.X, pady=(0, 15))

        # عرض معلومات الفاتورة إذا كانت موجودة
        if self.invoice_data:
            self.create_invoice_info(main_frame)

        # ملاحظة الحقول المطلوبة
        note_label = tk.Label(main_frame, text="* حقول مطلوبة", font=self.fonts['normal'], fg="red")
        note_label.pack(anchor=tk.W, pady=(0, 15))

        # أزرار العمليات
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        # زر الحفظ
        save_text = "إضافة الدفعة" if self.invoice_data else "إنشاء الفاتورة"
        save_btn = tk.Button(buttons_frame, text=save_text, font=self.fonts['button'],
                            bg="#28A745", fg="white", command=self.save_invoice, width=15)
        save_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="إلغاء", font=self.fonts['button'],
                              bg="#6C757D", fg="white", command=self.cancel, width=12)
        cancel_btn.pack(side=tk.RIGHT)

        # ربط Enter بالحفظ
        self.dialog.bind('<Return>', lambda e: self.save_invoice())
        self.dialog.bind('<Escape>', lambda e: self.cancel())

    def create_invoice_info(self, parent):
        """إنشاء معلومات الفاتورة الحالية"""
        info_frame = tk.LabelFrame(parent, text="معلومات الفاتورة", font=self.fonts['normal'])
        info_frame.pack(fill=tk.X, pady=(0, 15))

        # رقم الفاتورة
        tk.Label(info_frame, text=f"رقم الفاتورة: {self.invoice_data.get('invoice_id', '')}",
                font=self.fonts['normal']).pack(anchor=tk.W, padx=10, pady=2)

        # العميل
        tk.Label(info_frame, text=f"العميل: {self.invoice_data.get('customer_name', '')}",
                font=self.fonts['normal']).pack(anchor=tk.W, padx=10, pady=2)

        # إجمالي المبلغ
        tk.Label(info_frame, text=f"إجمالي المبلغ: {self.invoice_data.get('total_amount', 0):.2f}",
                font=self.fonts['normal']).pack(anchor=tk.W, padx=10, pady=2)

        # المبلغ المدفوع
        tk.Label(info_frame, text=f"المبلغ المدفوع: {self.invoice_data.get('paid_amount', 0):.2f}",
                font=self.fonts['normal']).pack(anchor=tk.W, padx=10, pady=2)

        # المبلغ المتبقي
        remaining = self.invoice_data.get('total_amount', 0) - self.invoice_data.get('paid_amount', 0)
        tk.Label(info_frame, text=f"المبلغ المتبقي: {remaining:.2f}",
                font=self.fonts['normal'], fg="red" if remaining > 0 else "green").pack(anchor=tk.W, padx=10, pady=2)

    def populate_data(self):
        """تعبئة البيانات للتحديث"""
        # لا حاجة لتعبئة بيانات إضافية للفواتير الموجودة
        pass

    def validate_data(self):
        """التحقق من صحة البيانات"""
        # التحقق من اختيار العنصر (للفواتير الجديدة فقط)
        if not self.invoice_data and hasattr(self, 'item_combobox') and not self.item_combobox.get():
            item_name = "العينة" if self.invoice_type == 'sample' else "عملية الصب"
            messagebox.showerror("خطأ", f"يرجى اختيار {item_name}")
            return False

        # التحقق من تكلفة الصب (للفواتير الجديدة لعمليات الصب فقط)
        if (self.invoice_type == 'casting' and not self.invoice_data and
            hasattr(self, 'casting_cost_entry')):
            try:
                cost = float(self.casting_cost_entry.get())
                if cost < 0:
                    raise ValueError()
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال تكلفة صحيحة")
                self.casting_cost_entry.focus()
                return False

        # التحقق من المبلغ المدفوع
        try:
            paid_amount = float(self.paid_amount_entry.get() or "0")
            if paid_amount < 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
            self.paid_amount_entry.focus()
            return False

        return True

    def save_invoice(self):
        """حفظ بيانات الفاتورة"""
        if not self.validate_data():
            return

        # جمع البيانات الأساسية
        self.result = {
            'paid_amount': float(self.paid_amount_entry.get() or "0")
        }

        # إضافة بيانات إضافية للفواتير الجديدة
        if not self.invoice_data:
            if hasattr(self, 'item_combobox'):
                item_text = self.item_combobox.get()
                item_id = int(item_text.split(' - ')[0])

                if self.invoice_type == 'sample':
                    self.result['sample_id'] = item_id
                else:
                    self.result['casting_id'] = item_id
                    if hasattr(self, 'casting_cost_entry'):
                        self.result['casting_cost'] = float(self.casting_cost_entry.get())
        else:
            # إضافة دفعة لفاتورة موجودة
            self.result['invoice_id'] = self.invoice_data.get('invoice_id')

        self.dialog.destroy()

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()
