"""
نموذج العميل - يحتوي على جميع العمليات المتعلقة بإدارة العملاء
"""

from typing import List, Dict, Optional
from datetime import datetime
from database_manager import DatabaseManager


class Customer:
    """نموذج العميل"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        تهيئة نموذج العميل
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db = db_manager
    
    def add_customer(self, name: str, id_number: str, phone: str = None, address: str = None) -> int:
        """
        إضافة عميل جديد
        
        Args:
            name: اسم العميل
            id_number: رقم الهوية
            phone: رقم الهاتف (اختياري)
            address: العنوان (اختياري)
            
        Returns:
            معرف العميل الجديد
            
        Raises:
            ValueError: إذا كان رقم الهوية مكرر
        """
        # التحقق من عدم تكرار رقم الهوية
        existing = self.get_customer_by_id_number(id_number)
        if existing:
            raise ValueError(f"رقم الهوية {id_number} موجود مسبقاً")
        
        query = """
            INSERT INTO Customers (name, id_number, phone, address)
            VALUES (?, ?, ?, ?)
        """
        return self.db.execute_update(query, (name, id_number, phone, address))
    
    def get_customer_by_id(self, customer_id: int) -> Optional[Dict]:
        """
        الحصول على عميل بواسطة المعرف
        
        Args:
            customer_id: معرف العميل
            
        Returns:
            بيانات العميل أو None إذا لم يوجد
        """
        query = "SELECT * FROM Customers WHERE customer_id = ?"
        results = self.db.execute_query(query, (customer_id,))
        return dict(results[0]) if results else None
    
    def get_customer_by_id_number(self, id_number: str) -> Optional[Dict]:
        """
        الحصول على عميل بواسطة رقم الهوية
        
        Args:
            id_number: رقم الهوية
            
        Returns:
            بيانات العميل أو None إذا لم يوجد
        """
        query = "SELECT * FROM Customers WHERE id_number = ?"
        results = self.db.execute_query(query, (id_number,))
        return dict(results[0]) if results else None
    
    def search_customers(self, search_term: str) -> List[Dict]:
        """
        البحث عن العملاء بالاسم أو رقم الهوية
        
        Args:
            search_term: مصطلح البحث
            
        Returns:
            قائمة بالعملاء المطابقين
        """
        query = """
            SELECT * FROM Customers 
            WHERE name LIKE ? OR id_number LIKE ?
            ORDER BY name
        """
        search_pattern = f"%{search_term}%"
        results = self.db.execute_query(query, (search_pattern, search_pattern))
        return [dict(row) for row in results]
    
    def get_all_customers(self, limit: int = None) -> List[Dict]:
        """
        الحصول على جميع العملاء
        
        Args:
            limit: حد أقصى لعدد النتائج
            
        Returns:
            قائمة بجميع العملاء
        """
        query = "SELECT * FROM Customers ORDER BY name"
        if limit:
            query += f" LIMIT {limit}"
        
        results = self.db.execute_query(query)
        return [dict(row) for row in results]
    
    def update_customer(self, customer_id: int, name: str = None, phone: str = None, address: str = None) -> bool:
        """
        تحديث بيانات عميل
        
        Args:
            customer_id: معرف العميل
            name: الاسم الجديد (اختياري)
            phone: رقم الهاتف الجديد (اختياري)
            address: العنوان الجديد (اختياري)
            
        Returns:
            True إذا تم التحديث بنجاح
        """
        # بناء الاستعلام ديناميكياً
        updates = []
        params = []
        
        if name is not None:
            updates.append("name = ?")
            params.append(name)
        
        if phone is not None:
            updates.append("phone = ?")
            params.append(phone)
        
        if address is not None:
            updates.append("address = ?")
            params.append(address)
        
        if not updates:
            return False
        
        params.append(customer_id)
        query = f"UPDATE Customers SET {', '.join(updates)} WHERE customer_id = ?"
        
        rows_affected = self.db.execute_update(query, tuple(params))
        return rows_affected > 0
    
    def delete_customer(self, customer_id: int) -> bool:
        """
        حذف عميل
        
        Args:
            customer_id: معرف العميل
            
        Returns:
            True إذا تم الحذف بنجاح
        """
        # التحقق من وجود عمليات مرتبطة بالعميل
        samples = self.db.execute_query(
            "SELECT COUNT(*) as count FROM Samples WHERE customer_id = ?",
            (customer_id,)
        )
        
        if samples[0]['count'] > 0:
            raise ValueError("لا يمكن حذف العميل لوجود عمليات مرتبطة به")
        
        query = "DELETE FROM Customers WHERE customer_id = ?"
        rows_affected = self.db.execute_update(query, (customer_id,))
        return rows_affected > 0
    
    def get_customer_operations(self, customer_id: int) -> Dict:
        """
        الحصول على جميع العمليات المرتبطة بعميل
        
        Args:
            customer_id: معرف العميل
            
        Returns:
            قاموس يحتوي على العينات والصب والفواتير
        """
        # العينات
        samples_query = """
            SELECT s.*, e.name as employee_name
            FROM Samples s
            LEFT JOIN Employees e ON s.employee_id = e.employee_id
            WHERE s.customer_id = ?
            ORDER BY s.analysis_date DESC
        """
        samples = self.db.execute_query(samples_query, (customer_id,))
        
        # الصب
        castings_query = """
            SELECT c.*, e.name as employee_name
            FROM Castings c
            LEFT JOIN Employees e ON c.employee_id = e.employee_id
            WHERE c.customer_id = ?
            ORDER BY c.casting_date DESC
        """
        castings = self.db.execute_query(castings_query, (customer_id,))
        
        # الفواتير
        invoices_query = """
            SELECT * FROM Invoices
            WHERE customer_id = ?
            ORDER BY invoice_date DESC
        """
        invoices = self.db.execute_query(invoices_query, (customer_id,))
        
        return {
            'samples': [dict(row) for row in samples],
            'castings': [dict(row) for row in castings],
            'invoices': [dict(row) for row in invoices]
        }
    
    def get_customer_financial_summary(self, customer_id: int) -> Dict:
        """
        الحصول على ملخص مالي للعميل
        
        Args:
            customer_id: معرف العميل
            
        Returns:
            ملخص مالي يحتوي على إجمالي المبالغ والمدفوعات والمستحقات
        """
        query = """
            SELECT 
                COUNT(*) as total_invoices,
                SUM(total_amount) as total_amount,
                SUM(paid_amount) as total_paid,
                SUM(total_amount - paid_amount) as outstanding_amount
            FROM Invoices
            WHERE customer_id = ?
        """
        result = self.db.execute_query(query, (customer_id,))
        
        if result:
            summary = dict(result[0])
            # تعامل مع القيم الفارغة
            for key in ['total_amount', 'total_paid', 'outstanding_amount']:
                if summary[key] is None:
                    summary[key] = 0.0
            return summary
        
        return {
            'total_invoices': 0,
            'total_amount': 0.0,
            'total_paid': 0.0,
            'outstanding_amount': 0.0
        }

