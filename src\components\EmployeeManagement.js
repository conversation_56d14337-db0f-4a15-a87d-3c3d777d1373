import React, { useState, useEffect } from 'react';

const EmployeeManagement = () => {
  const [employees, setEmployees] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    id_number: '',
    role: '',
    phone: ''
  });
  const [message, setMessage] = useState({ type: '', text: '' });

  const roles = [
    { value: 'محلل', label: 'محلل' },
    { value: 'فني تنقية', label: 'فني تنقية' },
    { value: 'فني صب', label: 'فني صب' },
    { value: 'إداري', label: 'إداري' },
    { value: 'مدير', label: 'مدير' }
  ];

  useEffect(() => {
    loadEmployees();
  }, []);

  const loadEmployees = async () => {
    setIsLoading(true);
    
    // محاكاة استدعاء API
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // بيانات تجريبية
    const mockEmployees = [
      {
        employee_id: 1,
        name: 'سارة أحمد المحمد',
        id_number: '2234567890',
        role: 'محلل',
        phone: '0502234567',
        created_at: '2024-12-01',
        total_operations: 45,
        performance_rating: 4.8
      },
      {
        employee_id: 2,
        name: 'محمد علي السالم',
        id_number: '3345678901',
        role: 'فني تنقية',
        phone: '0503345678',
        created_at: '2024-11-15',
        total_operations: 32,
        performance_rating: 4.6
      },
      {
        employee_id: 3,
        name: 'فاطمة عبدالله الأحمد',
        id_number: '4456789012',
        role: 'فني صب',
        phone: '0504456789',
        created_at: '2024-10-20',
        total_operations: 28,
        performance_rating: 4.7
      },
      {
        employee_id: 4,
        name: 'أحمد محمد الخالد',
        id_number: '5567890123',
        role: 'إداري',
        phone: '0505567890',
        created_at: '2024-09-10',
        total_operations: 0,
        performance_rating: 4.5
      }
    ];
    
    setEmployees(mockEmployees);
    setIsLoading(false);
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      // محاكاة استدعاء API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      if (editingEmployee) {
        // تحديث موظف موجود
        setEmployees(employees.map(employee => 
          employee.employee_id === editingEmployee.employee_id 
            ? { ...employee, ...formData }
            : employee
        ));
        setMessage({ type: 'success', text: 'تم تحديث بيانات الموظف بنجاح' });
      } else {
        // إضافة موظف جديد
        const newEmployee = {
          employee_id: employees.length + 1,
          ...formData,
          created_at: new Date().toISOString().split('T')[0],
          total_operations: 0,
          performance_rating: 0
        };
        setEmployees([...employees, newEmployee]);
        setMessage({ type: 'success', text: 'تم إضافة الموظف بنجاح' });
      }
      
      resetForm();
    } catch (error) {
      setMessage({ type: 'error', text: 'حدث خطأ أثناء حفظ البيانات' });
    }
  };

  const handleEdit = (employee) => {
    setEditingEmployee(employee);
    setFormData({
      name: employee.name,
      id_number: employee.id_number,
      role: employee.role,
      phone: employee.phone
    });
    setShowAddForm(true);
  };

  const handleDelete = async (employeeId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
      try {
        // محاكاة استدعاء API
        await new Promise(resolve => setTimeout(resolve, 500));
        
        setEmployees(employees.filter(employee => employee.employee_id !== employeeId));
        setMessage({ type: 'success', text: 'تم حذف الموظف بنجاح' });
      } catch (error) {
        setMessage({ type: 'error', text: 'حدث خطأ أثناء حذف الموظف' });
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      id_number: '',
      role: '',
      phone: ''
    });
    setEditingEmployee(null);
    setShowAddForm(false);
  };

  const filteredEmployees = employees.filter(employee =>
    employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.id_number.includes(searchTerm) ||
    employee.role.includes(searchTerm) ||
    employee.phone.includes(searchTerm)
  );

  const getRoleIcon = (role) => {
    switch (role) {
      case 'محلل':
        return '🔬';
      case 'فني تنقية':
        return '⚗️';
      case 'فني صب':
        return '🏭';
      case 'إداري':
        return '📋';
      case 'مدير':
        return '👨‍💼';
      default:
        return '👤';
    }
  };

  const getPerformanceColor = (rating) => {
    if (rating >= 4.5) return '#28a745';
    if (rating >= 4.0) return '#ffc107';
    if (rating >= 3.5) return '#fd7e14';
    return '#dc3545';
  };

  // إخفاء الرسالة بعد 3 ثوان
  useEffect(() => {
    if (message.text) {
      const timer = setTimeout(() => {
        setMessage({ type: '', text: '' });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  if (isLoading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="employee-management">
      <div className="card">
        <div className="card-header">
          <h1 className="card-title">إدارة الموظفين</h1>
          <button 
            className="btn btn-primary"
            onClick={() => setShowAddForm(true)}
          >
            ➕ إضافة موظف جديد
          </button>
        </div>

        {message.text && (
          <div className={`alert alert-${message.type === 'success' ? 'success' : 'error'}`}>
            {message.text}
          </div>
        )}

        {/* نموذج إضافة/تعديل الموظف */}
        {showAddForm && (
          <div className="card" style={{ marginBottom: '20px' }}>
            <div className="card-header">
              <h3>{editingEmployee ? 'تعديل بيانات الموظف' : 'إضافة موظف جديد'}</h3>
              <button 
                className="btn btn-secondary"
                onClick={resetForm}
              >
                ✕ إلغاء
              </button>
            </div>
            
            <form onSubmit={handleSubmit}>
              <div className="form-row">
                <div className="form-col">
                  <div className="form-group">
                    <label>اسم الموظف *</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      placeholder="أدخل اسم الموظف"
                    />
                  </div>
                </div>
                
                <div className="form-col">
                  <div className="form-group">
                    <label>رقم الهوية *</label>
                    <input
                      type="text"
                      name="id_number"
                      value={formData.id_number}
                      onChange={handleInputChange}
                      required
                      placeholder="أدخل رقم الهوية"
                    />
                  </div>
                </div>
              </div>
              
              <div className="form-row">
                <div className="form-col">
                  <div className="form-group">
                    <label>الوظيفة *</label>
                    <select
                      name="role"
                      value={formData.role}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="">اختر الوظيفة</option>
                      {roles.map((role) => (
                        <option key={role.value} value={role.value}>
                          {role.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                
                <div className="form-col">
                  <div className="form-group">
                    <label>رقم الهاتف</label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="أدخل رقم الهاتف"
                    />
                  </div>
                </div>
              </div>
              
              <div className="form-actions">
                <button type="submit" className="btn btn-success">
                  {editingEmployee ? '💾 حفظ التغييرات' : '➕ إضافة الموظف'}
                </button>
                <button type="button" className="btn btn-secondary" onClick={resetForm}>
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        )}

        {/* البحث */}
        <div className="form-group" style={{ marginBottom: '20px' }}>
          <input
            type="text"
            placeholder="البحث بالاسم أو رقم الهوية أو الوظيفة أو الهاتف..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{ width: '100%', maxWidth: '400px' }}
          />
        </div>

        {/* إحصائيات سريعة */}
        <div className="stats-grid" style={{ marginBottom: '20px' }}>
          <div className="stat-card">
            <div className="stat-value">{employees.length}</div>
            <div className="stat-label">إجمالي الموظفين</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-value">{employees.filter(e => e.role === 'محلل').length}</div>
            <div className="stat-label">محللين</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-value">{employees.filter(e => e.role === 'فني تنقية').length}</div>
            <div className="stat-label">فنيي تنقية</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-value">{employees.filter(e => e.role === 'فني صب').length}</div>
            <div className="stat-label">فنيي صب</div>
          </div>
        </div>

        {/* جدول الموظفين */}
        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>الاسم</th>
                <th>رقم الهوية</th>
                <th>الوظيفة</th>
                <th>الهاتف</th>
                <th>عدد العمليات</th>
                <th>تقييم الأداء</th>
                <th>تاريخ التوظيف</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredEmployees.map((employee) => (
                <tr key={employee.employee_id}>
                  <td>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                      <span style={{ fontSize: '20px' }}>
                        {getRoleIcon(employee.role)}
                      </span>
                      {employee.name}
                    </div>
                  </td>
                  <td>{employee.id_number}</td>
                  <td>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '4px',
                      backgroundColor: '#f0f0f0',
                      fontSize: '12px'
                    }}>
                      {employee.role}
                    </span>
                  </td>
                  <td>{employee.phone || '-'}</td>
                  <td>{employee.total_operations}</td>
                  <td>
                    {employee.performance_rating > 0 ? (
                      <span style={{
                        color: getPerformanceColor(employee.performance_rating),
                        fontWeight: 'bold'
                      }}>
                        ⭐ {employee.performance_rating.toFixed(1)}
                      </span>
                    ) : (
                      <span style={{ color: '#999' }}>غير محدد</span>
                    )}
                  </td>
                  <td>{employee.created_at}</td>
                  <td>
                    <div style={{ display: 'flex', gap: '5px' }}>
                      <button
                        className="btn btn-warning"
                        onClick={() => handleEdit(employee)}
                        style={{ padding: '5px 10px', fontSize: '12px' }}
                      >
                        ✏️ تعديل
                      </button>
                      <button
                        className="btn btn-danger"
                        onClick={() => handleDelete(employee.employee_id)}
                        style={{ padding: '5px 10px', fontSize: '12px' }}
                      >
                        🗑️ حذف
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredEmployees.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
            {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد موظفين مسجلين'}
          </div>
        )}
      </div>
    </div>
  );
};

export default EmployeeManagement;

