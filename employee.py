"""
نموذج الموظف - يحتوي على جميع العمليات المتعلقة بإدارة الموظفين
"""

from typing import List, Dict, Optional
from datetime import datetime
from database.database_manager import DatabaseManager


class Employee:
    """نموذج الموظف"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        تهيئة نموذج الموظف
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db = db_manager
    
    def add_employee(self, name: str, id_number: str, role: str, phone: str = None) -> int:
        """
        إضافة موظف جديد
        
        Args:
            name: اسم الموظف
            id_number: رقم الهوية
            role: الوظيفة (محلل، فني تنقية، فني صب، إداري)
            phone: رقم الهاتف (اختياري)
            
        Returns:
            معرف الموظف الجديد
            
        Raises:
            ValueError: إذا كان رقم الهوية مكرر
        """
        # التحقق من عدم تكرار رقم الهوية
        existing = self.get_employee_by_id_number(id_number)
        if existing:
            raise ValueError(f"رقم الهوية {id_number} موجود مسبقاً")
        
        # التحقق من صحة الوظيفة
        valid_roles = ['محلل', 'فني تنقية', 'فني صب', 'إداري', 'مدير']
        if role not in valid_roles:
            raise ValueError(f"الوظيفة يجب أن تكون إحدى: {', '.join(valid_roles)}")
        
        query = """
            INSERT INTO Employees (name, id_number, role, phone)
            VALUES (?, ?, ?, ?)
        """
        return self.db.execute_update(query, (name, id_number, role, phone))
    
    def get_employee_by_id(self, employee_id: int) -> Optional[Dict]:
        """
        الحصول على موظف بواسطة المعرف
        
        Args:
            employee_id: معرف الموظف
            
        Returns:
            بيانات الموظف أو None إذا لم يوجد
        """
        query = "SELECT * FROM Employees WHERE employee_id = ?"
        results = self.db.execute_query(query, (employee_id,))
        return dict(results[0]) if results else None
    
    def get_employee_by_id_number(self, id_number: str) -> Optional[Dict]:
        """
        الحصول على موظف بواسطة رقم الهوية
        
        Args:
            id_number: رقم الهوية
            
        Returns:
            بيانات الموظف أو None إذا لم يوجد
        """
        query = "SELECT * FROM Employees WHERE id_number = ?"
        results = self.db.execute_query(query, (id_number,))
        return dict(results[0]) if results else None
    
    def search_employees(self, search_term: str) -> List[Dict]:
        """
        البحث عن الموظفين بالاسم أو رقم الهوية أو الوظيفة
        
        Args:
            search_term: مصطلح البحث
            
        Returns:
            قائمة بالموظفين المطابقين
        """
        query = """
            SELECT * FROM Employees 
            WHERE name LIKE ? OR id_number LIKE ? OR role LIKE ?
            ORDER BY name
        """
        search_pattern = f"%{search_term}%"
        results = self.db.execute_query(query, (search_pattern, search_pattern, search_pattern))
        return [dict(row) for row in results]
    
    def get_all_employees(self, limit: int = None) -> List[Dict]:
        """
        الحصول على جميع الموظفين
        
        Args:
            limit: حد أقصى لعدد النتائج
            
        Returns:
            قائمة بجميع الموظفين
        """
        query = "SELECT * FROM Employees ORDER BY name"
        if limit:
            query += f" LIMIT {limit}"
        
        results = self.db.execute_query(query)
        return [dict(row) for row in results]
    
    def get_employees_by_role(self, role: str) -> List[Dict]:
        """
        الحصول على الموظفين حسب الوظيفة
        
        Args:
            role: الوظيفة
            
        Returns:
            قائمة بالموظفين في هذه الوظيفة
        """
        query = "SELECT * FROM Employees WHERE role = ? ORDER BY name"
        results = self.db.execute_query(query, (role,))
        return [dict(row) for row in results]
    
    def update_employee(self, employee_id: int, name: str = None, role: str = None, phone: str = None) -> bool:
        """
        تحديث بيانات موظف
        
        Args:
            employee_id: معرف الموظف
            name: الاسم الجديد (اختياري)
            role: الوظيفة الجديدة (اختياري)
            phone: رقم الهاتف الجديد (اختياري)
            
        Returns:
            True إذا تم التحديث بنجاح
        """
        # التحقق من صحة الوظيفة إذا تم تمريرها
        if role is not None:
            valid_roles = ['محلل', 'فني تنقية', 'فني صب', 'إداري', 'مدير']
            if role not in valid_roles:
                raise ValueError(f"الوظيفة يجب أن تكون إحدى: {', '.join(valid_roles)}")
        
        # بناء الاستعلام ديناميكياً
        updates = []
        params = []
        
        if name is not None:
            updates.append("name = ?")
            params.append(name)
        
        if role is not None:
            updates.append("role = ?")
            params.append(role)
        
        if phone is not None:
            updates.append("phone = ?")
            params.append(phone)
        
        if not updates:
            return False
        
        params.append(employee_id)
        query = f"UPDATE Employees SET {', '.join(updates)} WHERE employee_id = ?"
        
        rows_affected = self.db.execute_update(query, tuple(params))
        return rows_affected > 0
    
    def delete_employee(self, employee_id: int) -> bool:
        """
        حذف موظف
        
        Args:
            employee_id: معرف الموظف
            
        Returns:
            True إذا تم الحذف بنجاح
        """
        # التحقق من وجود عمليات مرتبطة بالموظف
        operations_count = 0
        
        # فحص العينات
        samples = self.db.execute_query(
            "SELECT COUNT(*) as count FROM Samples WHERE employee_id = ?",
            (employee_id,)
        )
        operations_count += samples[0]['count']
        
        # فحص التنقية
        refinements = self.db.execute_query(
            "SELECT COUNT(*) as count FROM Refinements WHERE employee_id = ?",
            (employee_id,)
        )
        operations_count += refinements[0]['count']
        
        # فحص الصب
        castings = self.db.execute_query(
            "SELECT COUNT(*) as count FROM Castings WHERE employee_id = ?",
            (employee_id,)
        )
        operations_count += castings[0]['count']
        
        if operations_count > 0:
            raise ValueError("لا يمكن حذف الموظف لوجود عمليات مرتبطة به")
        
        query = "DELETE FROM Employees WHERE employee_id = ?"
        rows_affected = self.db.execute_update(query, (employee_id,))
        return rows_affected > 0
    
    def get_employee_performance(self, employee_id: int, start_date: str = None, end_date: str = None) -> Dict:
        """
        الحصول على تقرير أداء الموظف
        
        Args:
            employee_id: معرف الموظف
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            
        Returns:
            تقرير أداء يحتوي على عدد العمليات المختلفة
        """
        date_filter = ""
        params = [employee_id]
        
        if start_date and end_date:
            date_filter = " AND DATE(analysis_date) BETWEEN ? AND ?"
            params.extend([start_date, end_date])
        
        # عدد العينات المحللة
        samples_query = f"""
            SELECT COUNT(*) as count FROM Samples 
            WHERE employee_id = ?{date_filter.replace('analysis_date', 'analysis_date')}
        """
        samples_count = self.db.execute_query(samples_query, params)[0]['count']
        
        # عدد عمليات التنقية
        refinements_query = f"""
            SELECT COUNT(*) as count FROM Refinements 
            WHERE employee_id = ?{date_filter.replace('analysis_date', 'refinement_date')}
        """
        refinements_count = self.db.execute_query(refinements_query, params)[0]['count']
        
        # عدد عمليات الصب
        castings_query = f"""
            SELECT COUNT(*) as count FROM Castings 
            WHERE employee_id = ?{date_filter.replace('analysis_date', 'casting_date')}
        """
        castings_count = self.db.execute_query(castings_query, params)[0]['count']
        
        return {
            'samples_analyzed': samples_count,
            'refinements_performed': refinements_count,
            'castings_performed': castings_count,
            'total_operations': samples_count + refinements_count + castings_count
        }
    
    def get_all_employees_performance(self, start_date: str = None, end_date: str = None) -> List[Dict]:
        """
        الحصول على تقرير أداء جميع الموظفين
        
        Args:
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            
        Returns:
            قائمة بتقارير أداء جميع الموظفين
        """
        employees = self.get_all_employees()
        performance_reports = []
        
        for employee in employees:
            performance = self.get_employee_performance(
                employee['employee_id'], start_date, end_date
            )
            performance_reports.append({
                'employee_id': employee['employee_id'],
                'name': employee['name'],
                'role': employee['role'],
                **performance
            })
        
        # ترتيب حسب إجمالي العمليات
        performance_reports.sort(key=lambda x: x['total_operations'], reverse=True)
        return performance_reports

