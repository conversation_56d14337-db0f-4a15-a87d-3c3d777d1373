import React, { useState, useEffect } from 'react';

const InvoiceManagement = () => {
  const [invoices, setInvoices] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState({
    customer_id: '',
    total_amount: '',
    description: '',
    paid_amount: 0
  });
  const [paymentData, setPaymentData] = useState({
    payment_amount: ''
  });
  const [message, setMessage] = useState({ type: '', text: '' });

  const statusOptions = [
    { value: 'all', label: 'جميع الفواتير' },
    { value: 'مدفوع', label: 'مدفوع' },
    { value: 'غير مدفوع', label: 'غير مدفوع' },
    { value: 'مدفوع جزئياً', label: 'مدفوع جزئياً' },
    { value: 'ملغى', label: 'ملغى' }
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    
    // محاكاة استدعاء API
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // بيانات تجريبية
    const mockCustomers = [
      { customer_id: 1, name: 'أحمد محمد علي' },
      { customer_id: 2, name: 'فاطمة أحمد السالم' },
      { customer_id: 3, name: 'محمد عبدالله الأحمد' }
    ];

    const mockInvoices = [
      {
        invoice_id: 1,
        customer_id: 1,
        customer_name: 'أحمد محمد علي',
        total_amount: 750,
        paid_amount: 750,
        outstanding_amount: 0,
        status: 'مدفوع',
        invoice_date: '2025-07-01',
        description: 'تحليل عينة ذهب خام 25.5 غرام',
        invoice_number: 'INV-2025-000001'
      },
      {
        invoice_id: 2,
        customer_id: 2,
        customer_name: 'فاطمة أحمد السالم',
        total_amount: 1200,
        paid_amount: 600,
        outstanding_amount: 600,
        status: 'مدفوع جزئياً',
        invoice_date: '2025-06-28',
        description: 'صب سبيكة ذهب 50 غرام',
        invoice_number: 'INV-2025-000002'
      },
      {
        invoice_id: 3,
        customer_id: 3,
        customer_name: 'محمد عبدالله الأحمد',
        total_amount: 950,
        paid_amount: 0,
        outstanding_amount: 950,
        status: 'غير مدفوع',
        invoice_date: '2025-06-25',
        description: 'تحليل وتنقية عينة مجوهرات',
        invoice_number: 'INV-2025-000003'
      },
      {
        invoice_id: 4,
        customer_id: 1,
        customer_name: 'أحمد محمد علي',
        total_amount: 2500,
        paid_amount: 0,
        outstanding_amount: 2500,
        status: 'غير مدفوع',
        invoice_date: '2025-07-01',
        description: 'صب سبيكة ذهب 100 غرام',
        invoice_number: 'INV-2025-000004'
      }
    ];

    setCustomers(mockCustomers);
    setInvoices(mockInvoices);
    setIsLoading(false);
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handlePaymentInputChange = (e) => {
    setPaymentData({
      ...paymentData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      // محاكاة استدعاء API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const customerName = customers.find(c => c.customer_id == formData.customer_id)?.name || '';
      const totalAmount = parseFloat(formData.total_amount);
      const paidAmount = parseFloat(formData.paid_amount) || 0;
      const outstandingAmount = totalAmount - paidAmount;
      
      let status = 'غير مدفوع';
      if (paidAmount >= totalAmount) {
        status = 'مدفوع';
      } else if (paidAmount > 0) {
        status = 'مدفوع جزئياً';
      }

      const newInvoice = {
        invoice_id: invoices.length + 1,
        customer_id: formData.customer_id,
        customer_name: customerName,
        total_amount: totalAmount,
        paid_amount: paidAmount,
        outstanding_amount: outstandingAmount,
        status: status,
        invoice_date: new Date().toISOString().split('T')[0],
        description: formData.description,
        invoice_number: `INV-2025-${(invoices.length + 1).toString().padStart(6, '0')}`
      };

      setInvoices([...invoices, newInvoice]);
      setMessage({ type: 'success', text: 'تم إنشاء الفاتورة بنجاح' });
      resetForm();
    } catch (error) {
      setMessage({ type: 'error', text: 'حدث خطأ أثناء إنشاء الفاتورة' });
    }
  };

  const handlePaymentSubmit = async (e) => {
    e.preventDefault();
    
    try {
      // محاكاة استدعاء API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const paymentAmount = parseFloat(paymentData.payment_amount);
      const updatedInvoices = invoices.map(invoice => {
        if (invoice.invoice_id === selectedInvoice.invoice_id) {
          const newPaidAmount = invoice.paid_amount + paymentAmount;
          const newOutstandingAmount = invoice.total_amount - newPaidAmount;
          
          let newStatus = 'غير مدفوع';
          if (newPaidAmount >= invoice.total_amount) {
            newStatus = 'مدفوع';
          } else if (newPaidAmount > 0) {
            newStatus = 'مدفوع جزئياً';
          }

          return {
            ...invoice,
            paid_amount: newPaidAmount,
            outstanding_amount: newOutstandingAmount,
            status: newStatus
          };
        }
        return invoice;
      });

      setInvoices(updatedInvoices);
      setMessage({ type: 'success', text: 'تم إضافة الدفعة بنجاح' });
      setShowPaymentForm(false);
      setSelectedInvoice(null);
      setPaymentData({ payment_amount: '' });
    } catch (error) {
      setMessage({ type: 'error', text: 'حدث خطأ أثناء إضافة الدفعة' });
    }
  };

  const handleAddPayment = (invoice) => {
    setSelectedInvoice(invoice);
    setShowPaymentForm(true);
  };

  const handleUpdateStatus = async (invoiceId, newStatus) => {
    try {
      // محاكاة استدعاء API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setInvoices(invoices.map(invoice => 
        invoice.invoice_id === invoiceId 
          ? { ...invoice, status: newStatus }
          : invoice
      ));

      setMessage({ type: 'success', text: 'تم تحديث حالة الفاتورة بنجاح' });
    } catch (error) {
      setMessage({ type: 'error', text: 'حدث خطأ أثناء تحديث الفاتورة' });
    }
  };

  const resetForm = () => {
    setFormData({
      customer_id: '',
      total_amount: '',
      description: '',
      paid_amount: 0
    });
    setShowAddForm(false);
  };

  const filteredInvoices = invoices.filter(invoice => {
    const matchesStatus = filterStatus === 'all' || invoice.status === filterStatus;
    const matchesSearch = invoice.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesStatus && matchesSearch;
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'مدفوع':
        return '#28a745';
      case 'مدفوع جزئياً':
        return '#ffc107';
      case 'غير مدفوع':
        return '#dc3545';
      case 'ملغى':
        return '#6c757d';
      default:
        return '#6c757d';
    }
  };

  const getTotalStats = () => {
    const totalInvoices = invoices.length;
    const totalAmount = invoices.reduce((sum, inv) => sum + inv.total_amount, 0);
    const totalPaid = invoices.reduce((sum, inv) => sum + inv.paid_amount, 0);
    const totalOutstanding = invoices.reduce((sum, inv) => sum + inv.outstanding_amount, 0);
    const paidInvoices = invoices.filter(inv => inv.status === 'مدفوع').length;
    const unpaidInvoices = invoices.filter(inv => inv.status === 'غير مدفوع').length;

    return {
      totalInvoices,
      totalAmount,
      totalPaid,
      totalOutstanding,
      paidInvoices,
      unpaidInvoices
    };
  };

  const stats = getTotalStats();

  // إخفاء الرسالة بعد 3 ثوان
  useEffect(() => {
    if (message.text) {
      const timer = setTimeout(() => {
        setMessage({ type: '', text: '' });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  if (isLoading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="invoice-management">
      <div className="card">
        <div className="card-header">
          <h1 className="card-title">إدارة الفواتير</h1>
          <button 
            className="btn btn-primary"
            onClick={() => setShowAddForm(true)}
          >
            ➕ إنشاء فاتورة جديدة
          </button>
        </div>

        {message.text && (
          <div className={`alert alert-${message.type === 'success' ? 'success' : 'error'}`}>
            {message.text}
          </div>
        )}

        {/* إحصائيات الفواتير */}
        <div className="stats-grid" style={{ marginBottom: '20px' }}>
          <div className="stat-card">
            <div className="stat-value">{stats.totalInvoices}</div>
            <div className="stat-label">إجمالي الفواتير</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-value">{stats.totalAmount.toLocaleString()} ر.س</div>
            <div className="stat-label">إجمالي المبالغ</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-value">{stats.totalPaid.toLocaleString()} ر.س</div>
            <div className="stat-label">المبالغ المدفوعة</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-value">{stats.totalOutstanding.toLocaleString()} ر.س</div>
            <div className="stat-label">المبالغ المستحقة</div>
          </div>
        </div>

        {/* نموذج إنشاء فاتورة جديدة */}
        {showAddForm && (
          <div className="card" style={{ marginBottom: '20px' }}>
            <div className="card-header">
              <h3>إنشاء فاتورة جديدة</h3>
              <button 
                className="btn btn-secondary"
                onClick={resetForm}
              >
                ✕ إلغاء
              </button>
            </div>
            
            <form onSubmit={handleSubmit}>
              <div className="form-row">
                <div className="form-col">
                  <div className="form-group">
                    <label>العميل *</label>
                    <select
                      name="customer_id"
                      value={formData.customer_id}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="">اختر العميل</option>
                      {customers.map(customer => (
                        <option key={customer.customer_id} value={customer.customer_id}>
                          {customer.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                
                <div className="form-col">
                  <div className="form-group">
                    <label>المبلغ الإجمالي (ر.س) *</label>
                    <input
                      type="number"
                      name="total_amount"
                      value={formData.total_amount}
                      onChange={handleInputChange}
                      required
                      step="0.01"
                      placeholder="أدخل المبلغ"
                    />
                  </div>
                </div>
              </div>
              
              <div className="form-row">
                <div className="form-col">
                  <div className="form-group">
                    <label>وصف الخدمة *</label>
                    <input
                      type="text"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      required
                      placeholder="أدخل وصف الخدمة"
                    />
                  </div>
                </div>
                
                <div className="form-col">
                  <div className="form-group">
                    <label>المبلغ المدفوع مقدماً (ر.س)</label>
                    <input
                      type="number"
                      name="paid_amount"
                      value={formData.paid_amount}
                      onChange={handleInputChange}
                      step="0.01"
                      placeholder="0"
                    />
                  </div>
                </div>
              </div>
              
              <div className="form-actions">
                <button type="submit" className="btn btn-success">
                  ➕ إنشاء الفاتورة
                </button>
                <button type="button" className="btn btn-secondary" onClick={resetForm}>
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        )}

        {/* نموذج إضافة دفعة */}
        {showPaymentForm && selectedInvoice && (
          <div className="card" style={{ marginBottom: '20px' }}>
            <div className="card-header">
              <h3>إضافة دفعة للفاتورة #{selectedInvoice.invoice_number}</h3>
              <button 
                className="btn btn-secondary"
                onClick={() => {
                  setShowPaymentForm(false);
                  setSelectedInvoice(null);
                  setPaymentData({ payment_amount: '' });
                }}
              >
                ✕ إلغاء
              </button>
            </div>
            
            <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
              <p><strong>العميل:</strong> {selectedInvoice.customer_name}</p>
              <p><strong>المبلغ الإجمالي:</strong> {selectedInvoice.total_amount.toLocaleString()} ر.س</p>
              <p><strong>المبلغ المدفوع:</strong> {selectedInvoice.paid_amount.toLocaleString()} ر.س</p>
              <p><strong>المبلغ المستحق:</strong> {selectedInvoice.outstanding_amount.toLocaleString()} ر.س</p>
            </div>
            
            <form onSubmit={handlePaymentSubmit}>
              <div className="form-group">
                <label>مبلغ الدفعة (ر.س) *</label>
                <input
                  type="number"
                  name="payment_amount"
                  value={paymentData.payment_amount}
                  onChange={handlePaymentInputChange}
                  required
                  step="0.01"
                  max={selectedInvoice.outstanding_amount}
                  placeholder="أدخل مبلغ الدفعة"
                />
              </div>
              
              <div className="form-actions">
                <button type="submit" className="btn btn-success">
                  💰 إضافة الدفعة
                </button>
                <button 
                  type="button" 
                  className="btn btn-secondary" 
                  onClick={() => {
                    setShowPaymentForm(false);
                    setSelectedInvoice(null);
                    setPaymentData({ payment_amount: '' });
                  }}
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        )}

        {/* فلاتر البحث */}
        <div style={{ display: 'flex', gap: '20px', marginBottom: '20px', alignItems: 'center' }}>
          <div className="form-group" style={{ margin: 0, flex: 1 }}>
            <input
              type="text"
              placeholder="البحث بالعميل أو رقم الفاتورة أو الوصف..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="form-group" style={{ margin: 0, minWidth: '200px' }}>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              {statusOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* جدول الفواتير */}
        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>رقم الفاتورة</th>
                <th>العميل</th>
                <th>الوصف</th>
                <th>المبلغ الإجمالي</th>
                <th>المبلغ المدفوع</th>
                <th>المبلغ المستحق</th>
                <th>التاريخ</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredInvoices.map((invoice) => (
                <tr key={invoice.invoice_id}>
                  <td>{invoice.invoice_number}</td>
                  <td>{invoice.customer_name}</td>
                  <td>{invoice.description}</td>
                  <td>{invoice.total_amount.toLocaleString()} ر.س</td>
                  <td>{invoice.paid_amount.toLocaleString()} ر.س</td>
                  <td style={{ 
                    color: invoice.outstanding_amount > 0 ? '#dc3545' : '#28a745',
                    fontWeight: 'bold'
                  }}>
                    {invoice.outstanding_amount.toLocaleString()} ر.س
                  </td>
                  <td>{invoice.invoice_date}</td>
                  <td>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '4px',
                      backgroundColor: getStatusColor(invoice.status),
                      color: 'white',
                      fontSize: '12px'
                    }}>
                      {invoice.status}
                    </span>
                  </td>
                  <td>
                    <div style={{ display: 'flex', gap: '5px', flexWrap: 'wrap' }}>
                      {invoice.outstanding_amount > 0 && (
                        <button
                          className="btn btn-success"
                          onClick={() => handleAddPayment(invoice)}
                          style={{ padding: '5px 10px', fontSize: '12px' }}
                        >
                          💰 دفع
                        </button>
                      )}
                      
                      {invoice.status !== 'ملغى' && (
                        <button
                          className="btn btn-danger"
                          onClick={() => handleUpdateStatus(invoice.invoice_id, 'ملغى')}
                          style={{ padding: '5px 10px', fontSize: '12px' }}
                        >
                          ❌ إلغاء
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredInvoices.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
            {searchTerm || filterStatus !== 'all' ? 'لا توجد نتائج للبحث' : 'لا توجد فواتير مسجلة'}
          </div>
        )}
      </div>
    </div>
  );
};

export default InvoiceManagement;

