#!/usr/bin/env python3
"""
حوار إضافة وتعديل العملاء
Customer Add/Edit Dialog
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import font as tkFont

class CustomerDialog:
    """حوار إضافة/تعديل العملاء"""
    
    def __init__(self, parent, title, customer_data=None):
        """
        تهيئة الحوار
        
        Args:
            parent: النافذة الأب
            title: عنوان الحوار
            customer_data: بيانات العميل للتعديل (اختياري)
        """
        self.result = None
        self.customer_data = customer_data
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x350")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد الخطوط
        self.setup_fonts()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تعبئة البيانات إذا كانت متوفرة
        if customer_data:
            self.populate_data()
        
        # تركيز على أول حقل
        self.name_entry.focus()
        
        # انتظار إغلاق النافذة
        self.dialog.wait_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_fonts(self):
        """إعداد الخطوط"""
        self.fonts = {
            'title': tkFont.Font(family="Arial", size=14, weight="bold"),
            'normal': tkFont.Font(family="Arial", size=11),
            'button': tkFont.Font(family="Arial", size=10, weight="bold")
        }
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.dialog, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_text = "تعديل العميل" if self.customer_data else "إضافة عميل جديد"
        title_label = tk.Label(main_frame, text=title_text,
                              font=self.fonts['title'])
        title_label.pack(pady=(0, 20))
        
        # حقل الاسم
        tk.Label(main_frame, text="اسم العميل *:",
                font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        
        self.name_entry = tk.Entry(main_frame, font=self.fonts['normal'], width=40)
        self.name_entry.pack(fill=tk.X, pady=(0, 15))
        
        # حقل رقم الهوية
        tk.Label(main_frame, text="رقم الهوية *:",
                font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        
        self.id_number_entry = tk.Entry(main_frame, font=self.fonts['normal'], width=40)
        self.id_number_entry.pack(fill=tk.X, pady=(0, 15))
        
        # حقل رقم الهاتف
        tk.Label(main_frame, text="رقم الهاتف:",
                font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        
        self.phone_entry = tk.Entry(main_frame, font=self.fonts['normal'], width=40)
        self.phone_entry.pack(fill=tk.X, pady=(0, 15))
        
        # حقل العنوان
        tk.Label(main_frame, text="العنوان:",
                font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        
        self.address_text = tk.Text(main_frame, font=self.fonts['normal'], 
                                   width=40, height=4)
        self.address_text.pack(fill=tk.X, pady=(0, 20))
        
        # ملاحظة الحقول المطلوبة
        note_label = tk.Label(main_frame, text="* حقول مطلوبة",
                             font=self.fonts['normal'], fg="red")
        note_label.pack(anchor=tk.W, pady=(0, 15))
        
        # أزرار العمليات
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        # زر الحفظ
        save_text = "تحديث" if self.customer_data else "حفظ"
        save_btn = tk.Button(buttons_frame, text=save_text,
                            font=self.fonts['button'],
                            bg="#28A745", fg="white",
                            command=self.save_customer,
                            width=12)
        save_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="إلغاء",
                              font=self.fonts['button'],
                              bg="#6C757D", fg="white",
                              command=self.cancel,
                              width=12)
        cancel_btn.pack(side=tk.RIGHT)
        
        # ربط Enter بالحفظ
        self.dialog.bind('<Return>', lambda e: self.save_customer())
        self.dialog.bind('<Escape>', lambda e: self.cancel())
    
    def populate_data(self):
        """تعبئة البيانات للتعديل"""
        if not self.customer_data:
            return
        
        self.name_entry.insert(0, self.customer_data.get('name', ''))
        self.id_number_entry.insert(0, self.customer_data.get('id_number', ''))
        self.phone_entry.insert(0, self.customer_data.get('phone', ''))
        self.address_text.insert('1.0', self.customer_data.get('address', ''))
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        name = self.name_entry.get().strip()
        id_number = self.id_number_entry.get().strip()
        
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
            self.name_entry.focus()
            return False
        
        if not id_number:
            messagebox.showerror("خطأ", "يرجى إدخال رقم الهوية")
            self.id_number_entry.focus()
            return False
        
        # التحقق من طول رقم الهوية
        if len(id_number) < 5:
            messagebox.showerror("خطأ", "رقم الهوية يجب أن يكون 5 أرقام على الأقل")
            self.id_number_entry.focus()
            return False
        
        return True
    
    def save_customer(self):
        """حفظ بيانات العميل"""
        if not self.validate_data():
            return
        
        # جمع البيانات
        self.result = {
            'name': self.name_entry.get().strip(),
            'id_number': self.id_number_entry.get().strip(),
            'phone': self.phone_entry.get().strip() or None,
            'address': self.address_text.get('1.0', tk.END).strip() or None
        }
        
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()


class EmployeeDialog:
    """حوار إضافة/تعديل الموظفين"""
    
    def __init__(self, parent, title, employee_data=None):
        """
        تهيئة الحوار
        
        Args:
            parent: النافذة الأب
            title: عنوان الحوار
            employee_data: بيانات الموظف للتعديل (اختياري)
        """
        self.result = None
        self.employee_data = employee_data
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد الخطوط
        self.setup_fonts()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تعبئة البيانات إذا كانت متوفرة
        if employee_data:
            self.populate_data()
        
        # تركيز على أول حقل
        self.name_entry.focus()
        
        # انتظار إغلاق النافذة
        self.dialog.wait_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_fonts(self):
        """إعداد الخطوط"""
        self.fonts = {
            'title': tkFont.Font(family="Arial", size=14, weight="bold"),
            'normal': tkFont.Font(family="Arial", size=11),
            'button': tkFont.Font(family="Arial", size=10, weight="bold")
        }
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.dialog, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_text = "تعديل الموظف" if self.employee_data else "إضافة موظف جديد"
        title_label = tk.Label(main_frame, text=title_text,
                              font=self.fonts['title'])
        title_label.pack(pady=(0, 20))
        
        # حقل الاسم
        tk.Label(main_frame, text="اسم الموظف *:",
                font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        
        self.name_entry = tk.Entry(main_frame, font=self.fonts['normal'], width=40)
        self.name_entry.pack(fill=tk.X, pady=(0, 15))
        
        # حقل رقم الهوية
        tk.Label(main_frame, text="رقم الهوية *:",
                font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        
        self.id_number_entry = tk.Entry(main_frame, font=self.fonts['normal'], width=40)
        self.id_number_entry.pack(fill=tk.X, pady=(0, 15))
        
        # حقل المنصب
        tk.Label(main_frame, text="المنصب *:",
                font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        
        self.role_combobox = ttk.Combobox(main_frame, font=self.fonts['normal'],
                                         values=["محلل", "فني تنقية", "فني صب", "مشرف", "مدير"],
                                         state="readonly", width=37)
        self.role_combobox.pack(fill=tk.X, pady=(0, 15))
        
        # حقل رقم الهاتف
        tk.Label(main_frame, text="رقم الهاتف:",
                font=self.fonts['normal']).pack(anchor=tk.W, pady=(0, 5))
        
        self.phone_entry = tk.Entry(main_frame, font=self.fonts['normal'], width=40)
        self.phone_entry.pack(fill=tk.X, pady=(0, 20))
        
        # ملاحظة الحقول المطلوبة
        note_label = tk.Label(main_frame, text="* حقول مطلوبة",
                             font=self.fonts['normal'], fg="red")
        note_label.pack(anchor=tk.W, pady=(0, 15))
        
        # أزرار العمليات
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        # زر الحفظ
        save_text = "تحديث" if self.employee_data else "حفظ"
        save_btn = tk.Button(buttons_frame, text=save_text,
                            font=self.fonts['button'],
                            bg="#28A745", fg="white",
                            command=self.save_employee,
                            width=12)
        save_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="إلغاء",
                              font=self.fonts['button'],
                              bg="#6C757D", fg="white",
                              command=self.cancel,
                              width=12)
        cancel_btn.pack(side=tk.RIGHT)
        
        # ربط Enter بالحفظ
        self.dialog.bind('<Return>', lambda e: self.save_employee())
        self.dialog.bind('<Escape>', lambda e: self.cancel())
    
    def populate_data(self):
        """تعبئة البيانات للتعديل"""
        if not self.employee_data:
            return
        
        self.name_entry.insert(0, self.employee_data.get('name', ''))
        self.id_number_entry.insert(0, self.employee_data.get('id_number', ''))
        self.role_combobox.set(self.employee_data.get('role', ''))
        self.phone_entry.insert(0, self.employee_data.get('phone', ''))
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        name = self.name_entry.get().strip()
        id_number = self.id_number_entry.get().strip()
        role = self.role_combobox.get().strip()
        
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم الموظف")
            self.name_entry.focus()
            return False
        
        if not id_number:
            messagebox.showerror("خطأ", "يرجى إدخال رقم الهوية")
            self.id_number_entry.focus()
            return False
        
        if not role:
            messagebox.showerror("خطأ", "يرجى اختيار المنصب")
            self.role_combobox.focus()
            return False
        
        return True
    
    def save_employee(self):
        """حفظ بيانات الموظف"""
        if not self.validate_data():
            return
        
        # جمع البيانات
        self.result = {
            'name': self.name_entry.get().strip(),
            'id_number': self.id_number_entry.get().strip(),
            'role': self.role_combobox.get().strip(),
            'phone': self.phone_entry.get().strip() or None
        }
        
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()
