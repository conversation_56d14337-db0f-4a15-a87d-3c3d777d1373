import React, { useState } from 'react';

const LoginForm = ({ onLogin }) => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    setError(''); // مسح الخطأ عند تغيير الإدخال
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // محاكاة استدعاء API لتسجيل الدخول
      // في التطبيق الحقيقي، ستقوم بإرسال طلب إلى الخادم
      await new Promise(resolve => setTimeout(resolve, 1000));

      // التحقق من بيانات الاعتماد (مؤقت)
      if (formData.username === 'admin' && formData.password === 'admin123') {
        const userData = {
          username: formData.username,
          role: 'admin',
          name: 'مدير النظام'
        };
        onLogin(userData);
      } else {
        setError('اسم المستخدم أو كلمة المرور غير صحيحة');
      }
    } catch (err) {
      setError('حدث خطأ أثناء تسجيل الدخول');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="login-container">
      <form className="login-form" onSubmit={handleSubmit}>
        <h2>🏆 نظام إدارة معمل الذهب</h2>
        
        {error && (
          <div className="alert alert-error">
            {error}
          </div>
        )}

        <div className="form-group">
          <label htmlFor="username">اسم المستخدم</label>
          <input
            type="text"
            id="username"
            name="username"
            value={formData.username}
            onChange={handleChange}
            required
            disabled={isLoading}
            placeholder="أدخل اسم المستخدم"
          />
        </div>

        <div className="form-group">
          <label htmlFor="password">كلمة المرور</label>
          <input
            type="password"
            id="password"
            name="password"
            value={formData.password}
            onChange={handleChange}
            required
            disabled={isLoading}
            placeholder="أدخل كلمة المرور"
          />
        </div>

        <button
          type="submit"
          className="login-btn"
          disabled={isLoading}
        >
          {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
        </button>

        <div style={{ marginTop: '20px', fontSize: '14px', color: '#666', textAlign: 'center' }}>
          <p>بيانات تجريبية:</p>
          <p>اسم المستخدم: admin</p>
          <p>كلمة المرور: admin123</p>
        </div>
      </form>
    </div>
  );
};

export default LoginForm;

