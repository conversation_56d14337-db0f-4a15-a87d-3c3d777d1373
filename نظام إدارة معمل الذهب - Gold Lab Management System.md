# نظام إدارة معمل الذهب - Gold Lab Management System

## نظرة عامة

نظام إدارة معمل الذهب هو تطبيق ويب شامل مطور خصيصاً لإدارة جميع عمليات معامل الذهب والمعادن النفيسة. يوفر النظام واجهة عربية حديثة وسهلة الاستخدام مع إمكانيات متقدمة لإدارة العملاء والموظفين والعمليات التقنية والفواتير والتقارير.

## الميزات الرئيسية

### 🏆 إدارة شاملة
- **إدارة العملاء**: قاعدة بيانات شاملة مع تتبع تاريخ التعاملات
- **إدارة الموظفين**: نظام متكامل مع تقييم الأداء
- **إدارة العمليات**: تحليل العينات، التنقية، والصب
- **إدارة الفواتير**: نظام فوترة متقدم مع تتبع المدفوعات

### 📊 تقارير وإحصائيات
- تقارير يومية وأسبوعية وشهرية ومخصصة
- إحصائيات مالية وتشغيلية مفصلة
- مؤشرات أداء رئيسية (KPIs)
- تصدير التقارير بصيغ متعددة

### 🔒 أمان وموثوقية
- نظام تسجيل دخول آمن
- تشفير البيانات الحساسة
- نسخ احتياطية تلقائية
- تسجيل جميع العمليات للمراجعة

### 🌐 واجهة حديثة
- تصميم عربي متجاوب
- دعم جميع الأجهزة والشاشات
- واجهة سهلة الاستخدام
- تحديث فوري للبيانات

## التقنيات المستخدمة

### Frontend
- **React.js 18**: مكتبة JavaScript للواجهة الأمامية
- **CSS3**: تصميم متجاوب وحديث
- **JavaScript ES6+**: برمجة حديثة ومتقدمة

### Backend
- **Python 3.11**: لغة البرمجة الأساسية
- **SQLite**: قاعدة بيانات محلية موثوقة
- **JSON**: تبادل البيانات

### Tools & Utilities
- **npm**: إدارة الحزم
- **Git**: نظام التحكم في الإصدارات
- **VS Code**: بيئة التطوير

## هيكل المشروع

```
gold_lab_management/
├── database/
│   ├── database_manager.py      # مدير قاعدة البيانات
│   └── gold_lab.db             # ملف قاعدة البيانات
├── models/
│   ├── customer.py             # نموذج العميل
│   ├── employee.py             # نموذج الموظف
│   ├── operations.py           # نموذج العمليات
│   └── invoice.py              # نموذج الفواتير
├── controllers/
│   └── main_controller.py      # المتحكم الرئيسي
├── gold-lab-frontend/
│   ├── src/
│   │   ├── components/         # مكونات React
│   │   ├── App.js             # التطبيق الرئيسي
│   │   └── App.css            # ملف الأنماط
│   ├── public/                # الملفات العامة
│   └── build/                 # ملفات الإنتاج
├── دليل_المستخدم_الشامل.md    # دليل المستخدم
└── README.md                  # هذا الملف
```

## متطلبات التشغيل

### متطلبات النظام
- **نظام التشغيل**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **الذاكرة**: 4 GB RAM (8 GB موصى به)
- **المساحة**: 2 GB مساحة فارغة
- **المتصفح**: Chrome 90+, Firefox 88+, Edge 90+, Safari 14+

### متطلبات التطوير
- **Node.js**: 18.0.0 أو أحدث
- **Python**: 3.11 أو أحدث
- **npm**: 8.0.0 أو أحدث

## التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-repo/gold-lab-management.git
cd gold-lab-management
```

### 2. تثبيت تبعيات Python
```bash
pip install sqlite3
```

### 3. تثبيت تبعيات React
```bash
cd gold-lab-frontend
npm install
```

### 4. تشغيل التطبيق للتطوير
```bash
npm start
```

### 5. بناء التطبيق للإنتاج
```bash
npm run build
```

## بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

> ⚠️ **تحذير**: يجب تغيير كلمة المرور الافتراضية فور تسجيل الدخول لأول مرة.

## الاستخدام

### تسجيل الدخول
1. افتح المتصفح وانتقل إلى `http://localhost:3000`
2. أدخل بيانات تسجيل الدخول
3. انقر على "تسجيل الدخول"

### إدارة العملاء
- انقر على "إدارة العملاء" في الشريط الجانبي
- استخدم زر "إضافة عميل جديد" لإضافة عملاء
- استخدم خاصية البحث للعثور على عملاء محددين

### إدارة العمليات
- انتقل إلى "إدارة العمليات"
- اختر نوع العملية (تحليل، تنقية، صب)
- املأ البيانات المطلوبة واحفظ

### إنشاء التقارير
- انتقل إلى قسم "التقارير"
- اختر نوع التقرير المطلوب
- حدد الفترة الزمنية
- انقر على "تحديث التقرير"

## قاعدة البيانات

### الجداول الرئيسية

#### جدول العملاء (customers)
```sql
CREATE TABLE customers (
    customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    id_number TEXT UNIQUE NOT NULL,
    phone TEXT,
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### جدول الموظفين (employees)
```sql
CREATE TABLE employees (
    employee_id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    id_number TEXT UNIQUE NOT NULL,
    role TEXT NOT NULL,
    phone TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### جدول تحليل العينات (sample_analysis)
```sql
CREATE TABLE sample_analysis (
    sample_id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER,
    weight REAL NOT NULL,
    material_type TEXT NOT NULL,
    purity_percentage REAL,
    analysis_cost REAL,
    analysis_date DATE,
    employee_id INTEGER,
    FOREIGN KEY (customer_id) REFERENCES customers (customer_id),
    FOREIGN KEY (employee_id) REFERENCES employees (employee_id)
);
```

#### جدول الفواتير (invoices)
```sql
CREATE TABLE invoices (
    invoice_id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER,
    total_amount REAL NOT NULL,
    paid_amount REAL DEFAULT 0,
    outstanding_amount REAL,
    status TEXT DEFAULT 'غير مدفوع',
    invoice_date DATE,
    description TEXT,
    FOREIGN KEY (customer_id) REFERENCES customers (customer_id)
);
```

## API والوظائف

### وظائف إدارة العملاء
- `add_customer(name, id_number, phone, address)`: إضافة عميل جديد
- `update_customer(customer_id, data)`: تحديث بيانات عميل
- `delete_customer(customer_id)`: حذف عميل
- `search_customers(query)`: البحث في العملاء

### وظائف إدارة العمليات
- `add_sample_analysis(data)`: إضافة عملية تحليل
- `add_refinement(data)`: إضافة عملية تنقية
- `add_casting(data)`: إضافة عملية صب
- `get_operations_by_type(type)`: جلب العمليات حسب النوع

### وظائف إدارة الفواتير
- `create_invoice(data)`: إنشاء فاتورة جديدة
- `add_payment(invoice_id, amount)`: إضافة دفعة
- `update_invoice_status(invoice_id, status)`: تحديث حالة الفاتورة
- `get_pending_invoices()`: جلب الفواتير المعلقة

## الأمان

### تشفير البيانات
- كلمات المرور محفوظة بتشفير SHA-256
- البيانات الحساسة محمية بطبقات أمان متعددة
- جلسات آمنة مع انتهاء صلاحية تلقائي

### النسخ الاحتياطية
- نسخ احتياطية تلقائية يومية
- إمكانية إنشاء نسخ احتياطية يدوية
- استعادة البيانات من النسخ الاحتياطية

### تسجيل العمليات
- جميع العمليات الحساسة مسجلة
- تتبع المستخدم والوقت لكل عملية
- سجلات للمراجعة والتدقيق

## التطوير والمساهمة

### إعداد بيئة التطوير
1. استنسخ المشروع
2. ثبت التبعيات
3. أنشئ فرع جديد للتطوير
4. اختبر التغييرات
5. أرسل طلب دمج (Pull Request)

### معايير الكود
- استخدم أسماء متغيرات وصفية
- اكتب تعليقات واضحة
- اتبع معايير JavaScript و Python
- اختبر الكود قبل الإرسال

### الاختبار
```bash
# اختبار React
cd gold-lab-frontend
npm test

# اختبار Python
python -m pytest tests/
```

## الدعم والمساعدة

### التوثيق
- [دليل المستخدم الشامل](./دليل_المستخدم_الشامل.md)
- [وثائق API](./docs/api.md)
- [دليل التطوير](./docs/development.md)

### الدعم التقني
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-11-1234567
- **ساعات العمل**: الأحد - الخميس، 8:00 ص - 5:00 م

### المشاكل الشائعة
راجع قسم "استكشاف الأخطاء وإصلاحها" في دليل المستخدم للحلول السريعة.

## الترخيص

هذا المشروع محمي بحقوق الطبع والنشر © 2025 نظام إدارة معمل الذهب. جميع الحقوق محفوظة.

## تاريخ الإصدارات

### الإصدار 1.0.0 (1 يوليو 2025)
- الإصدار الأولي
- جميع الميزات الأساسية
- واجهة عربية كاملة
- نظام تقارير شامل

## المطورون

- **Manus AI**: التطوير الأساسي والتصميم
- **فريق الاختبار**: ضمان الجودة والاختبار
- **فريق التوثيق**: إعداد الأدلة والوثائق

---

**للمزيد من المعلومات، راجع [دليل المستخدم الشامل](./دليل_المستخدم_الشامل.md)**

