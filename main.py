#!/usr/bin/env python3
"""
تطبيق نظام إدارة معمل الذهب
Gold Lab Management System Main Application
"""

import sys
import os
from main_controller import MainController

def print_menu():
    """طباعة القائمة الرئيسية"""
    print("\n" + "="*50)
    print("🏆 نظام إدارة معمل الذهب - Gold Lab Management System")
    print("="*50)
    print("1. تسجيل الدخول")
    print("2. عرض لوحة التحكم")
    print("3. إدارة العملاء")
    print("4. إدارة الموظفين")
    print("5. إدارة العمليات")
    print("6. إدارة الفواتير")
    print("7. التقارير")
    print("8. إنشاء نسخة احتياطية")
    print("9. خروج")
    print("="*50)

def login_menu(controller):
    """قائمة تسجيل الدخول"""
    print("\n🔐 تسجيل الدخول")
    print("-" * 30)
    username = input("اسم المستخدم: ")
    password = input("كلمة المرور: ")
    
    if controller.authenticate_user(username, password):
        print("✅ تم تسجيل الدخول بنجاح!")
        print(f"مرحباً {controller.current_user['username']}")
        return True
    else:
        print("❌ خطأ في اسم المستخدم أو كلمة المرور")
        return False

def dashboard_menu(controller):
    """عرض لوحة التحكم"""
    print("\n📊 لوحة التحكم")
    print("-" * 30)
    
    try:
        dashboard_data = controller.get_dashboard_data()
        
        print(f"📈 إجمالي العملاء: {dashboard_data['total_customers']}")
        print(f"👥 إجمالي الموظفين: {dashboard_data['total_employees']}")
        
        # عرض العمليات المعلقة
        pending = dashboard_data['pending_operations']
        print(f"⏳ العينات المعلقة: {len(pending.get('pending_samples', []))}")
        print(f"🔄 عمليات التنقية المعلقة: {len(pending.get('pending_refinements', []))}")
        
        # عرض الفواتير غير المدفوعة
        unpaid = dashboard_data['unpaid_invoices']
        print(f"💰 الفواتير غير المدفوعة: {len(unpaid)}")
        
        if unpaid:
            total_unpaid = sum(invoice.get('remaining_amount', 0) for invoice in unpaid)
            print(f"💸 إجمالي المبلغ غير المدفوع: {total_unpaid:.2f}")
            
    except Exception as e:
        print(f"❌ خطأ في تحميل لوحة التحكم: {e}")

def customer_menu(controller):
    """قائمة إدارة العملاء"""
    print("\n👥 إدارة العملاء")
    print("-" * 30)
    print("1. إضافة عميل جديد")
    print("2. البحث عن عميل")
    print("3. عرض تفاصيل عميل")
    print("4. العودة للقائمة الرئيسية")
    
    choice = input("اختر العملية: ")
    
    if choice == "1":
        # إضافة عميل جديد
        name = input("اسم العميل: ")
        id_number = input("رقم الهوية: ")
        phone = input("رقم الهاتف (اختياري): ") or None
        address = input("العنوان (اختياري): ") or None
        
        success, message, customer_id = controller.add_customer(name, id_number, phone, address)
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
            
    elif choice == "2":
        # البحث عن عميل
        search_term = input("أدخل اسم العميل أو رقم الهوية للبحث: ")
        customers = controller.search_customers(search_term)
        
        if customers:
            print(f"\n📋 تم العثور على {len(customers)} عميل:")
            for customer in customers:
                print(f"- {customer['name']} (ID: {customer['customer_id']}) - {customer['id_number']}")
        else:
            print("❌ لم يتم العثور على أي عميل")
            
    elif choice == "3":
        # عرض تفاصيل عميل
        try:
            customer_id = int(input("أدخل رقم العميل: "))
            details = controller.get_customer_details(customer_id)
            
            if details:
                customer = details['customer']
                print(f"\n📋 تفاصيل العميل:")
                print(f"الاسم: {customer['name']}")
                print(f"رقم الهوية: {customer['id_number']}")
                print(f"الهاتف: {customer.get('phone', 'غير محدد')}")
                print(f"العنوان: {customer.get('address', 'غير محدد')}")
                
                # عرض الملخص المالي
                financial = details['financial_summary']
                print(f"\n💰 الملخص المالي:")
                print(f"إجمالي الفواتير: {financial.get('total_invoices', 0)}")
                print(f"إجمالي المبلغ: {financial.get('total_amount', 0):.2f}")
                print(f"المبلغ المدفوع: {financial.get('paid_amount', 0):.2f}")
                print(f"المبلغ المتبقي: {financial.get('remaining_amount', 0):.2f}")
            else:
                print("❌ لم يتم العثور على العميل")
        except ValueError:
            print("❌ رقم العميل غير صحيح")

def operations_menu(controller):
    """قائمة إدارة العمليات"""
    print("\n⚙️ إدارة العمليات")
    print("-" * 30)
    print("1. إضافة عينة للتحليل")
    print("2. إكمال تحليل عينة")
    print("3. إضافة عملية تنقية")
    print("4. إكمال عملية تنقية")
    print("5. إضافة عملية صب")
    print("6. عرض العمليات المعلقة")
    print("7. العودة للقائمة الرئيسية")
    
    choice = input("اختر العملية: ")
    
    if choice == "1":
        # إضافة عينة للتحليل
        try:
            customer_id = int(input("رقم العميل: "))
            weight = float(input("وزن العينة (جرام): "))
            material_type = input("نوع المادة: ")
            
            success, message, sample_id = controller.add_sample_analysis(customer_id, weight, material_type)
            if success:
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")
        except ValueError:
            print("❌ خطأ في البيانات المدخلة")
            
    elif choice == "6":
        # عرض العمليات المعلقة
        pending = controller.get_pending_operations()
        
        print(f"\n⏳ العمليات المعلقة:")
        
        samples = pending.get('pending_samples', [])
        if samples:
            print(f"\n📋 العينات المعلقة ({len(samples)}):")
            for sample in samples:
                print(f"- عينة #{sample['sample_id']} - العميل: {sample.get('customer_name', 'غير محدد')} - الوزن: {sample['weight']}g")
        
        refinements = pending.get('pending_refinements', [])
        if refinements:
            print(f"\n🔄 عمليات التنقية المعلقة ({len(refinements)}):")
            for ref in refinements:
                print(f"- تنقية #{ref['refinement_id']} - العينة: #{ref['sample_id']} - الوزن الخام: {ref['raw_weight']}g")

def reports_menu(controller):
    """قائمة التقارير"""
    print("\n📊 التقارير")
    print("-" * 30)
    print("1. تقرير يومي")
    print("2. تقرير أسبوعي")
    print("3. تقرير شهري")
    print("4. العودة للقائمة الرئيسية")
    
    choice = input("اختر نوع التقرير: ")
    
    if choice == "1":
        # تقرير يومي
        date = input("أدخل التاريخ (YYYY-MM-DD) أو اتركه فارغاً لليوم الحالي: ") or None
        report = controller.get_daily_report(date)
        
        print(f"\n📅 التقرير اليومي - {report['date']}")
        print("-" * 40)
        
        ops = report['operations']
        print(f"العينات المضافة: {ops.get('samples_added', 0)}")
        print(f"العينات المكتملة: {ops.get('samples_completed', 0)}")
        print(f"عمليات التنقية: {ops.get('refinements_completed', 0)}")
        print(f"عمليات الصب: {ops.get('castings_completed', 0)}")
        
        financial = report['financial']
        print(f"\n💰 الملخص المالي:")
        print(f"الفواتير المنشأة: {financial.get('invoices_created', 0)}")
        print(f"إجمالي المبلغ: {financial.get('total_amount', 0):.2f}")
        print(f"المبلغ المحصل: {financial.get('collected_amount', 0):.2f}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل نظام إدارة معمل الذهب...")
    
    try:
        # إنشاء المتحكم الرئيسي
        controller = MainController()
        print("✅ تم تحميل النظام بنجاح!")
        print("\n📝 بيانات تسجيل الدخول الافتراضية:")
        print("اسم المستخدم: admin")
        print("كلمة المرور: admin123")
        
        while True:
            if not controller.is_authenticated():
                if not login_menu(controller):
                    continue
            
            print_menu()
            choice = input("\nاختر العملية: ")
            
            if choice == "1":
                if controller.is_authenticated():
                    print("✅ أنت مسجل دخول بالفعل")
                else:
                    login_menu(controller)
            elif choice == "2":
                if controller.is_authenticated():
                    dashboard_menu(controller)
                else:
                    print("❌ يجب تسجيل الدخول أولاً")
            elif choice == "3":
                if controller.is_authenticated():
                    customer_menu(controller)
                else:
                    print("❌ يجب تسجيل الدخول أولاً")
            elif choice == "4":
                if controller.is_authenticated():
                    print("👥 إدارة الموظفين - قيد التطوير")
                else:
                    print("❌ يجب تسجيل الدخول أولاً")
            elif choice == "5":
                if controller.is_authenticated():
                    operations_menu(controller)
                else:
                    print("❌ يجب تسجيل الدخول أولاً")
            elif choice == "6":
                if controller.is_authenticated():
                    print("💰 إدارة الفواتير - قيد التطوير")
                else:
                    print("❌ يجب تسجيل الدخول أولاً")
            elif choice == "7":
                if controller.is_authenticated():
                    reports_menu(controller)
                else:
                    print("❌ يجب تسجيل الدخول أولاً")
            elif choice == "8":
                if controller.is_authenticated():
                    success, message = controller.backup_database()
                    if success:
                        print(f"✅ {message}")
                    else:
                        print(f"❌ {message}")
                else:
                    print("❌ يجب تسجيل الدخول أولاً")
            elif choice == "9":
                print("👋 شكراً لاستخدام نظام إدارة معمل الذهب!")
                break
            else:
                print("❌ اختيار غير صحيح")
                
            input("\nاضغط Enter للمتابعة...")
            
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البرنامج: {e}")
        print("تأكد من وجود جميع الملفات المطلوبة")
    finally:
        print("🔄 تنظيف الموارد...")

if __name__ == "__main__":
    main()
