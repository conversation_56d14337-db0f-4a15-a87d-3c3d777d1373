#!/usr/bin/env python3
"""
تطبيق سطح المكتب لنظام إدارة معمل الذهب
Gold Lab Management System Desktop Application
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from tkinter import font as tkFont
import sys
import os
from datetime import datetime
from main_controller import MainController
from customer_dialog import CustomerDialog, EmployeeDialog

class GoldLabDesktopApp:
    """التطبيق الرئيسي لسطح المكتب"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.controller = None
        self.current_user = None
        
        # إنشاء النافذة الرئيسية
        self.root = tk.Tk()
        self.root.title("نظام إدارة معمل الذهب - Gold Lab Management System")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # تعيين الخط العربي
        self.setup_fonts()
        
        # تعيين الألوان والأنماط
        self.setup_styles()
        
        # إنشاء المتحكم
        try:
            self.controller = MainController()
            self.show_login_screen()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تهيئة النظام: {e}")
            self.root.destroy()
    
    def setup_fonts(self):
        """إعداد الخطوط"""
        self.fonts = {
            'title': tkFont.Font(family="Arial", size=16, weight="bold"),
            'header': tkFont.Font(family="Arial", size=14, weight="bold"),
            'normal': tkFont.Font(family="Arial", size=11),
            'small': tkFont.Font(family="Arial", size=9),
            'button': tkFont.Font(family="Arial", size=10, weight="bold")
        }
    
    def setup_styles(self):
        """إعداد الأنماط والألوان"""
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#28A745',
            'warning': '#FFC107',
            'danger': '#DC3545',
            'light': '#F8F9FA',
            'dark': '#343A40',
            'white': '#FFFFFF',
            'gold': '#FFD700'
        }
        
        # إعداد أنماط ttk
        style = ttk.Style()
        style.theme_use('clam')
        
        # أنماط الأزرار
        style.configure('Primary.TButton', 
                       background=self.colors['primary'],
                       foreground='white',
                       font=self.fonts['button'])
        
        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground='white',
                       font=self.fonts['button'])
        
        style.configure('Warning.TButton',
                       background=self.colors['warning'],
                       foreground='black',
                       font=self.fonts['button'])
        
        style.configure('Danger.TButton',
                       background=self.colors['danger'],
                       foreground='white',
                       font=self.fonts['button'])
    
    def clear_window(self):
        """مسح محتوى النافذة"""
        for widget in self.root.winfo_children():
            widget.destroy()
    
    def show_login_screen(self):
        """عرض شاشة تسجيل الدخول"""
        self.clear_window()
        
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # إطار تسجيل الدخول
        login_frame = tk.Frame(main_frame, bg=self.colors['white'], 
                              relief=tk.RAISED, bd=2)
        login_frame.place(relx=0.5, rely=0.5, anchor=tk.CENTER, 
                         width=400, height=300)
        
        # العنوان
        title_label = tk.Label(login_frame, 
                              text="🏆 نظام إدارة معمل الذهب",
                              font=self.fonts['title'],
                              bg=self.colors['white'],
                              fg=self.colors['primary'])
        title_label.pack(pady=20)
        
        subtitle_label = tk.Label(login_frame,
                                 text="Gold Lab Management System",
                                 font=self.fonts['normal'],
                                 bg=self.colors['white'],
                                 fg=self.colors['dark'])
        subtitle_label.pack(pady=(0, 30))
        
        # حقول الإدخال
        tk.Label(login_frame, text="اسم المستخدم:",
                font=self.fonts['normal'], bg=self.colors['white']).pack(pady=5)
        
        self.username_entry = tk.Entry(login_frame, font=self.fonts['normal'],
                                      width=25, justify='center')
        self.username_entry.pack(pady=5)
        
        tk.Label(login_frame, text="كلمة المرور:",
                font=self.fonts['normal'], bg=self.colors['white']).pack(pady=5)
        
        self.password_entry = tk.Entry(login_frame, font=self.fonts['normal'],
                                      width=25, show='*', justify='center')
        self.password_entry.pack(pady=5)
        
        # زر تسجيل الدخول
        login_btn = ttk.Button(login_frame, text="تسجيل الدخول",
                              style='Primary.TButton',
                              command=self.login)
        login_btn.pack(pady=20)
        
        # ربط Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda e: self.login())
        
        # تعيين القيم الافتراضية
        self.username_entry.insert(0, "admin")
        self.password_entry.insert(0, "admin123")
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        try:
            if self.controller.authenticate_user(username, password):
                self.current_user = self.controller.current_user
                messagebox.showinfo("نجح", f"مرحباً {self.current_user['username']}")
                self.show_main_screen()
            else:
                messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تسجيل الدخول: {e}")
    
    def show_main_screen(self):
        """عرض الشاشة الرئيسية"""
        self.clear_window()
        
        # إنشاء القائمة العلوية
        self.create_menu_bar()
        
        # إطار رئيسي
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # الشريط الجانبي
        self.create_sidebar(main_frame)
        
        # المحتوى الرئيسي
        self.content_frame = tk.Frame(main_frame, bg=self.colors['white'],
                                     relief=tk.SUNKEN, bd=1)
        self.content_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # عرض لوحة التحكم افتراضياً
        self.show_dashboard()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="نسخة احتياطية", command=self.backup_database)
        file_menu.add_separator()
        file_menu.add_command(label="تسجيل خروج", command=self.logout)
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة العرض
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="عرض", menu=view_menu)
        view_menu.add_command(label="لوحة التحكم", command=self.show_dashboard)
        view_menu.add_command(label="تحديث", command=self.refresh_current_view)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        sidebar = tk.Frame(parent, bg=self.colors['dark'], width=200)
        sidebar.pack(side=tk.LEFT, fill=tk.Y)
        sidebar.pack_propagate(False)
        
        # عنوان الشريط الجانبي
        title_label = tk.Label(sidebar, text="القوائم الرئيسية",
                              font=self.fonts['header'],
                              bg=self.colors['dark'],
                              fg=self.colors['white'])
        title_label.pack(pady=20)
        
        # أزرار القوائم
        buttons = [
            ("📊 لوحة التحكم", self.show_dashboard),
            ("👥 إدارة العملاء", self.show_customers),
            ("👨‍💼 إدارة الموظفين", self.show_employees),
            ("⚙️ إدارة العمليات", self.show_operations),
            ("💰 إدارة الفواتير", self.show_invoices),
            ("📈 التقارير", self.show_reports),
        ]
        
        for text, command in buttons:
            btn = tk.Button(sidebar, text=text,
                           font=self.fonts['normal'],
                           bg=self.colors['primary'],
                           fg=self.colors['white'],
                           relief=tk.FLAT,
                           width=20,
                           command=command)
            btn.pack(pady=5, padx=10, fill=tk.X)
            
            # تأثير hover
            btn.bind("<Enter>", lambda e, b=btn: b.config(bg=self.colors['secondary']))
            btn.bind("<Leave>", lambda e, b=btn: b.config(bg=self.colors['primary']))
    
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.clear_content()
        
        # عنوان الصفحة
        title_label = tk.Label(self.content_frame, text="📊 لوحة التحكم",
                              font=self.fonts['title'],
                              bg=self.colors['white'])
        title_label.pack(pady=20)
        
        try:
            # الحصول على بيانات لوحة التحكم
            dashboard_data = self.controller.get_dashboard_data()
            
            # إطار الإحصائيات
            stats_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
            stats_frame.pack(fill=tk.X, padx=20, pady=10)
            
            # بطاقات الإحصائيات
            self.create_stat_card(stats_frame, "إجمالي العملاء", 
                                 dashboard_data['total_customers'], 
                                 self.colors['primary'], 0, 0)
            
            self.create_stat_card(stats_frame, "إجمالي الموظفين",
                                 dashboard_data['total_employees'],
                                 self.colors['success'], 0, 1)
            
            pending_samples = len(dashboard_data['pending_operations'].get('pending_samples', []))
            self.create_stat_card(stats_frame, "العينات المعلقة",
                                 pending_samples,
                                 self.colors['warning'], 0, 2)
            
            unpaid_invoices = len(dashboard_data['unpaid_invoices'])
            self.create_stat_card(stats_frame, "الفواتير غير المدفوعة",
                                 unpaid_invoices,
                                 self.colors['danger'], 1, 0)
            
            # حساب إجمالي المبلغ غير المدفوع
            total_unpaid = sum(invoice.get('remaining_amount', 0) 
                             for invoice in dashboard_data['unpaid_invoices'])
            self.create_stat_card(stats_frame, "المبلغ غير المدفوع",
                                 f"{total_unpaid:.2f}",
                                 self.colors['secondary'], 1, 1)
            
            # التقرير اليومي
            today_report = dashboard_data['today_report']
            operations = today_report['operations']
            samples_today = operations.get('samples_added', 0)
            self.create_stat_card(stats_frame, "عينات اليوم",
                                 samples_today,
                                 self.colors['gold'], 1, 2)
            
        except Exception as e:
            error_label = tk.Label(self.content_frame, 
                                  text=f"خطأ في تحميل البيانات: {e}",
                                  font=self.fonts['normal'],
                                  fg=self.colors['danger'],
                                  bg=self.colors['white'])
            error_label.pack(pady=50)
    
    def create_stat_card(self, parent, title, value, color, row, col):
        """إنشاء بطاقة إحصائية"""
        card_frame = tk.Frame(parent, bg=color, relief=tk.RAISED, bd=2)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
        
        # تكوين الشبكة
        parent.grid_columnconfigure(col, weight=1)
        
        value_label = tk.Label(card_frame, text=str(value),
                              font=self.fonts['title'],
                              bg=color, fg=self.colors['white'])
        value_label.pack(pady=(10, 5))
        
        title_label = tk.Label(card_frame, text=title,
                              font=self.fonts['normal'],
                              bg=color, fg=self.colors['white'])
        title_label.pack(pady=(0, 10))
    
    def clear_content(self):
        """مسح المحتوى الحالي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def refresh_current_view(self):
        """تحديث العرض الحالي"""
        # يمكن تحسين هذا لاحقاً لتذكر العرض الحالي
        self.show_dashboard()
    
    def backup_database(self):
        """إنشاء نسخة احتياطية"""
        try:
            success, message = self.controller.backup_database()
            if success:
                messagebox.showinfo("نجح", message)
            else:
                messagebox.showerror("خطأ", message)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية: {e}")
    
    def logout(self):
        """تسجيل الخروج"""
        self.controller.logout_user()
        self.current_user = None
        self.show_login_screen()
    
    def show_customers(self):
        """عرض إدارة العملاء"""
        self.clear_content()

        # عنوان الصفحة
        title_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        title_frame.pack(fill=tk.X, padx=20, pady=10)

        title_label = tk.Label(title_frame, text="👥 إدارة العملاء",
                              font=self.fonts['title'],
                              bg=self.colors['white'])
        title_label.pack(side=tk.LEFT)

        # أزرار العمليات
        btn_frame = tk.Frame(title_frame, bg=self.colors['white'])
        btn_frame.pack(side=tk.RIGHT)

        add_btn = ttk.Button(btn_frame, text="إضافة عميل جديد",
                            style='Success.TButton',
                            command=self.add_customer_dialog)
        add_btn.pack(side=tk.LEFT, padx=5)

        refresh_btn = ttk.Button(btn_frame, text="تحديث",
                               style='Primary.TButton',
                               command=self.show_customers)
        refresh_btn.pack(side=tk.LEFT, padx=5)

        # إطار البحث
        search_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        search_frame.pack(fill=tk.X, padx=20, pady=5)

        tk.Label(search_frame, text="البحث:",
                font=self.fonts['normal'], bg=self.colors['white']).pack(side=tk.LEFT)

        self.customer_search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.customer_search_var,
                               font=self.fonts['normal'], width=30)
        search_entry.pack(side=tk.LEFT, padx=10)

        search_btn = ttk.Button(search_frame, text="بحث",
                               command=self.search_customers)
        search_btn.pack(side=tk.LEFT, padx=5)

        # جدول العملاء
        self.create_customers_table()

        # تحميل البيانات
        self.load_customers_data()

    def create_customers_table(self):
        """إنشاء جدول العملاء"""
        table_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # إنشاء Treeview
        columns = ('ID', 'الاسم', 'رقم الهوية', 'الهاتف', 'العنوان', 'تاريخ الإضافة')
        self.customers_tree = ttk.Treeview(table_frame, columns=columns, show='headings')

        # تعيين عناوين الأعمدة
        for col in columns:
            self.customers_tree.heading(col, text=col)
            self.customers_tree.column(col, width=120, anchor='center')

        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.customers_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.customers_tree.xview)
        self.customers_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # تخطيط الجدول
        self.customers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)

        # ربط النقر المزدوج
        self.customers_tree.bind('<Double-1>', self.edit_customer_dialog)

        # قائمة السياق
        self.create_customer_context_menu()

    def create_customer_context_menu(self):
        """إنشاء قائمة السياق للعملاء"""
        self.customer_context_menu = tk.Menu(self.root, tearoff=0)
        self.customer_context_menu.add_command(label="تعديل", command=self.edit_customer_dialog)
        self.customer_context_menu.add_command(label="حذف", command=self.delete_customer)
        self.customer_context_menu.add_separator()
        self.customer_context_menu.add_command(label="عرض العمليات", command=self.view_customer_operations)

        # ربط النقر بالزر الأيمن
        self.customers_tree.bind('<Button-3>', self.show_customer_context_menu)

    def show_customer_context_menu(self, event):
        """عرض قائمة السياق"""
        try:
            self.customer_context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.customer_context_menu.grab_release()

    def load_customers_data(self):
        """تحميل بيانات العملاء"""
        try:
            # مسح البيانات الحالية
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            # جلب العملاء
            customers = self.controller.customer_model.get_all_customers()

            for customer in customers:
                # تنسيق التاريخ
                created_at = customer.get('created_at', '')
                if created_at:
                    try:
                        date_obj = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                        created_at = date_obj.strftime('%Y-%m-%d')
                    except:
                        pass

                self.customers_tree.insert('', tk.END, values=(
                    customer.get('customer_id', ''),
                    customer.get('name', ''),
                    customer.get('id_number', ''),
                    customer.get('phone', ''),
                    customer.get('address', ''),
                    created_at
                ))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات العملاء: {e}")

    def search_customers(self):
        """البحث في العملاء"""
        search_term = self.customer_search_var.get().strip()

        try:
            # مسح البيانات الحالية
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            if search_term:
                customers = self.controller.customer_model.search_customers(search_term)
            else:
                customers = self.controller.customer_model.get_all_customers()

            for customer in customers:
                created_at = customer.get('created_at', '')
                if created_at:
                    try:
                        date_obj = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                        created_at = date_obj.strftime('%Y-%m-%d')
                    except:
                        pass

                self.customers_tree.insert('', tk.END, values=(
                    customer.get('customer_id', ''),
                    customer.get('name', ''),
                    customer.get('id_number', ''),
                    customer.get('phone', ''),
                    customer.get('address', ''),
                    created_at
                ))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {e}")

    def add_customer_dialog(self):
        """حوار إضافة عميل جديد"""
        dialog = CustomerDialog(self.root, "إضافة عميل جديد")
        if dialog.result:
            try:
                customer_id = self.controller.customer_model.add_customer(
                    dialog.result['name'],
                    dialog.result['id_number'],
                    dialog.result['phone'],
                    dialog.result['address']
                )
                messagebox.showinfo("نجح", f"تم إضافة العميل بنجاح برقم {customer_id}")
                self.load_customers_data()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة العميل: {e}")

    def edit_customer_dialog(self, event=None):
        """حوار تعديل عميل"""
        selected = self.customers_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للتعديل")
            return

        item = self.customers_tree.item(selected[0])
        values = item['values']

        customer_data = {
            'customer_id': values[0],
            'name': values[1],
            'id_number': values[2],
            'phone': values[3],
            'address': values[4]
        }

        dialog = CustomerDialog(self.root, "تعديل العميل", customer_data)
        if dialog.result:
            try:
                success = self.controller.customer_model.update_customer(
                    customer_data['customer_id'],
                    dialog.result
                )
                if success:
                    messagebox.showinfo("نجح", "تم تحديث بيانات العميل بنجاح")
                    self.load_customers_data()
                else:
                    messagebox.showerror("خطأ", "فشل في تحديث بيانات العميل")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحديث العميل: {e}")

    def delete_customer(self):
        """حذف عميل"""
        selected = self.customers_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للحذف")
            return

        item = self.customers_tree.item(selected[0])
        customer_id = item['values'][0]
        customer_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف",
                              f"هل أنت متأكد من حذف العميل '{customer_name}'؟"):
            try:
                success = self.controller.customer_model.delete_customer(customer_id)
                if success:
                    messagebox.showinfo("نجح", "تم حذف العميل بنجاح")
                    self.load_customers_data()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف العميل")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف العميل: {e}")

    def view_customer_operations(self):
        """عرض عمليات العميل"""
        selected = self.customers_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل")
            return

        item = self.customers_tree.item(selected[0])
        customer_id = item['values'][0]
        customer_name = item['values'][1]

        # يمكن تطوير هذا لاحقاً لعرض نافذة منفصلة بعمليات العميل
        messagebox.showinfo("عمليات العميل",
                           f"عرض عمليات العميل: {customer_name}\n"
                           f"رقم العميل: {customer_id}\n\n"
                           "هذه الميزة قيد التطوير...")

    def show_employees(self):
        """عرض إدارة الموظفين"""
        self.clear_content()

        # عنوان الصفحة
        title_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        title_frame.pack(fill=tk.X, padx=20, pady=10)

        title_label = tk.Label(title_frame, text="👨‍💼 إدارة الموظفين",
                              font=self.fonts['title'],
                              bg=self.colors['white'])
        title_label.pack(side=tk.LEFT)

        # أزرار العمليات
        btn_frame = tk.Frame(title_frame, bg=self.colors['white'])
        btn_frame.pack(side=tk.RIGHT)

        add_btn = ttk.Button(btn_frame, text="إضافة موظف جديد",
                            style='Success.TButton',
                            command=self.add_employee_dialog)
        add_btn.pack(side=tk.LEFT, padx=5)

        refresh_btn = ttk.Button(btn_frame, text="تحديث",
                               style='Primary.TButton',
                               command=self.show_employees)
        refresh_btn.pack(side=tk.LEFT, padx=5)

        # إطار البحث
        search_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        search_frame.pack(fill=tk.X, padx=20, pady=5)

        tk.Label(search_frame, text="البحث:",
                font=self.fonts['normal'], bg=self.colors['white']).pack(side=tk.LEFT)

        self.employee_search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.employee_search_var,
                               font=self.fonts['normal'], width=30)
        search_entry.pack(side=tk.LEFT, padx=10)

        search_btn = ttk.Button(search_frame, text="بحث",
                               command=self.search_employees)
        search_btn.pack(side=tk.LEFT, padx=5)

        # جدول الموظفين
        self.create_employees_table()

        # تحميل البيانات
        self.load_employees_data()

    def create_employees_table(self):
        """إنشاء جدول الموظفين"""
        table_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # إنشاء Treeview
        columns = ('ID', 'الاسم', 'رقم الهوية', 'المنصب', 'الهاتف', 'تاريخ الإضافة')
        self.employees_tree = ttk.Treeview(table_frame, columns=columns, show='headings')

        # تعيين عناوين الأعمدة
        for col in columns:
            self.employees_tree.heading(col, text=col)
            self.employees_tree.column(col, width=120, anchor='center')

        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.employees_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.employees_tree.xview)
        self.employees_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # تخطيط الجدول
        self.employees_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)

        # ربط النقر المزدوج
        self.employees_tree.bind('<Double-1>', self.edit_employee_dialog)

        # قائمة السياق
        self.create_employee_context_menu()

    def create_employee_context_menu(self):
        """إنشاء قائمة السياق للموظفين"""
        self.employee_context_menu = tk.Menu(self.root, tearoff=0)
        self.employee_context_menu.add_command(label="تعديل", command=self.edit_employee_dialog)
        self.employee_context_menu.add_command(label="حذف", command=self.delete_employee)
        self.employee_context_menu.add_separator()
        self.employee_context_menu.add_command(label="عرض الأداء", command=self.view_employee_performance)

        # ربط النقر بالزر الأيمن
        self.employees_tree.bind('<Button-3>', self.show_employee_context_menu)

    def show_employee_context_menu(self, event):
        """عرض قائمة السياق للموظفين"""
        try:
            self.employee_context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.employee_context_menu.grab_release()

    def load_employees_data(self):
        """تحميل بيانات الموظفين"""
        try:
            # مسح البيانات الحالية
            for item in self.employees_tree.get_children():
                self.employees_tree.delete(item)

            # جلب الموظفين
            employees = self.controller.employee_model.get_all_employees()

            for employee in employees:
                # تنسيق التاريخ
                created_at = employee.get('created_at', '')
                if created_at:
                    try:
                        date_obj = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                        created_at = date_obj.strftime('%Y-%m-%d')
                    except:
                        pass

                self.employees_tree.insert('', tk.END, values=(
                    employee.get('employee_id', ''),
                    employee.get('name', ''),
                    employee.get('id_number', ''),
                    employee.get('role', ''),
                    employee.get('phone', ''),
                    created_at
                ))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الموظفين: {e}")

    def search_employees(self):
        """البحث في الموظفين"""
        search_term = self.employee_search_var.get().strip()

        try:
            # مسح البيانات الحالية
            for item in self.employees_tree.get_children():
                self.employees_tree.delete(item)

            if search_term:
                employees = self.controller.employee_model.search_employees(search_term)
            else:
                employees = self.controller.employee_model.get_all_employees()

            for employee in employees:
                created_at = employee.get('created_at', '')
                if created_at:
                    try:
                        date_obj = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                        created_at = date_obj.strftime('%Y-%m-%d')
                    except:
                        pass

                self.employees_tree.insert('', tk.END, values=(
                    employee.get('employee_id', ''),
                    employee.get('name', ''),
                    employee.get('id_number', ''),
                    employee.get('role', ''),
                    employee.get('phone', ''),
                    created_at
                ))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {e}")

    def add_employee_dialog(self):
        """حوار إضافة موظف جديد"""
        dialog = EmployeeDialog(self.root, "إضافة موظف جديد")
        if dialog.result:
            try:
                employee_id = self.controller.employee_model.add_employee(
                    dialog.result['name'],
                    dialog.result['id_number'],
                    dialog.result['role'],
                    dialog.result['phone']
                )
                messagebox.showinfo("نجح", f"تم إضافة الموظف بنجاح برقم {employee_id}")
                self.load_employees_data()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة الموظف: {e}")

    def edit_employee_dialog(self, event=None):
        """حوار تعديل موظف"""
        selected = self.employees_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار موظف للتعديل")
            return

        item = self.employees_tree.item(selected[0])
        values = item['values']

        employee_data = {
            'employee_id': values[0],
            'name': values[1],
            'id_number': values[2],
            'role': values[3],
            'phone': values[4]
        }

        dialog = EmployeeDialog(self.root, "تعديل الموظف", employee_data)
        if dialog.result:
            try:
                success = self.controller.employee_model.update_employee(
                    employee_data['employee_id'],
                    dialog.result
                )
                if success:
                    messagebox.showinfo("نجح", "تم تحديث بيانات الموظف بنجاح")
                    self.load_employees_data()
                else:
                    messagebox.showerror("خطأ", "فشل في تحديث بيانات الموظف")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحديث الموظف: {e}")

    def delete_employee(self):
        """حذف موظف"""
        selected = self.employees_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار موظف للحذف")
            return

        item = self.employees_tree.item(selected[0])
        employee_id = item['values'][0]
        employee_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف",
                              f"هل أنت متأكد من حذف الموظف '{employee_name}'؟"):
            try:
                success = self.controller.employee_model.delete_employee(employee_id)
                if success:
                    messagebox.showinfo("نجح", "تم حذف الموظف بنجاح")
                    self.load_employees_data()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف الموظف")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف الموظف: {e}")

    def view_employee_performance(self):
        """عرض أداء الموظف"""
        selected = self.employees_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار موظف")
            return

        item = self.employees_tree.item(selected[0])
        employee_id = item['values'][0]
        employee_name = item['values'][1]

        # يمكن تطوير هذا لاحقاً لعرض نافذة منفصلة بأداء الموظف
        messagebox.showinfo("أداء الموظف",
                           f"عرض أداء الموظف: {employee_name}\n"
                           f"رقم الموظف: {employee_id}\n\n"
                           "هذه الميزة قيد التطوير...")

    def show_operations(self):
        """عرض إدارة العمليات"""
        self.clear_content()

        title_label = tk.Label(self.content_frame, text="⚙️ إدارة العمليات",
                              font=self.fonts['title'],
                              bg=self.colors['white'])
        title_label.pack(pady=20)

        # رسالة مؤقتة
        temp_label = tk.Label(self.content_frame,
                             text="قسم إدارة العمليات قيد التطوير...",
                             font=self.fonts['normal'],
                             bg=self.colors['white'])
        temp_label.pack(pady=50)

    def show_invoices(self):
        """عرض إدارة الفواتير"""
        self.clear_content()

        title_label = tk.Label(self.content_frame, text="💰 إدارة الفواتير",
                              font=self.fonts['title'],
                              bg=self.colors['white'])
        title_label.pack(pady=20)

        # رسالة مؤقتة
        temp_label = tk.Label(self.content_frame,
                             text="قسم إدارة الفواتير قيد التطوير...",
                             font=self.fonts['normal'],
                             bg=self.colors['white'])
        temp_label.pack(pady=50)

    def show_reports(self):
        """عرض التقارير"""
        self.clear_content()

        title_label = tk.Label(self.content_frame, text="📈 التقارير",
                              font=self.fonts['title'],
                              bg=self.colors['white'])
        title_label.pack(pady=20)

        # رسالة مؤقتة
        temp_label = tk.Label(self.content_frame,
                             text="قسم التقارير قيد التطوير...",
                             font=self.fonts['normal'],
                             bg=self.colors['white'])
        temp_label.pack(pady=50)

    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
نظام إدارة معمل الذهب
Gold Lab Management System

الإصدار: 1.0.0
تطوير: فريق التطوير

نظام شامل لإدارة معامل الذهب والمعادن النفيسة
يوفر إدارة العملاء والموظفين والعمليات والفواتير والتقارير

© 2025 جميع الحقوق محفوظة
        """
        messagebox.showinfo("حول البرنامج", about_text)
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        app = GoldLabDesktopApp()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
