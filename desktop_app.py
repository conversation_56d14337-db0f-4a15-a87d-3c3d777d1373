#!/usr/bin/env python3
"""
تطبيق سطح المكتب لنظام إدارة معمل الذهب
Gold Lab Management System Desktop Application
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from tkinter import font as tkFont
import sys
import os
from datetime import datetime
from main_controller import MainController
from customer_dialog import CustomerDialog, EmployeeDialog
from operations_dialogs import SampleAnalysisDialog, RefinementDialog, CastingDialog, InvoiceDialog

class GoldLabDesktopApp:
    """التطبيق الرئيسي لسطح المكتب"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.controller = None
        self.current_user = None
        
        # إنشاء النافذة الرئيسية
        self.root = tk.Tk()
        self.root.title("نظام إدارة معمل الذهب - Gold Lab Management System")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # تعيين الخط العربي
        self.setup_fonts()
        
        # تعيين الألوان والأنماط
        self.setup_styles()
        
        # إنشاء المتحكم
        try:
            self.controller = MainController()
            self.show_login_screen()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تهيئة النظام: {e}")
            self.root.destroy()
    
    def setup_fonts(self):
        """إعداد الخطوط"""
        self.fonts = {
            'title': tkFont.Font(family="Arial", size=16, weight="bold"),
            'header': tkFont.Font(family="Arial", size=14, weight="bold"),
            'normal': tkFont.Font(family="Arial", size=11),
            'small': tkFont.Font(family="Arial", size=9),
            'button': tkFont.Font(family="Arial", size=10, weight="bold")
        }
    
    def setup_styles(self):
        """إعداد الأنماط والألوان"""
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#28A745',
            'warning': '#FFC107',
            'danger': '#DC3545',
            'light': '#F8F9FA',
            'dark': '#343A40',
            'white': '#FFFFFF',
            'gold': '#FFD700'
        }
        
        # إعداد أنماط ttk
        style = ttk.Style()
        style.theme_use('clam')
        
        # أنماط الأزرار
        style.configure('Primary.TButton', 
                       background=self.colors['primary'],
                       foreground='white',
                       font=self.fonts['button'])
        
        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground='white',
                       font=self.fonts['button'])
        
        style.configure('Warning.TButton',
                       background=self.colors['warning'],
                       foreground='black',
                       font=self.fonts['button'])
        
        style.configure('Danger.TButton',
                       background=self.colors['danger'],
                       foreground='white',
                       font=self.fonts['button'])
    
    def clear_window(self):
        """مسح محتوى النافذة"""
        for widget in self.root.winfo_children():
            widget.destroy()
    
    def show_login_screen(self):
        """عرض شاشة تسجيل الدخول"""
        self.clear_window()
        
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # إطار تسجيل الدخول
        login_frame = tk.Frame(main_frame, bg=self.colors['white'], 
                              relief=tk.RAISED, bd=2)
        login_frame.place(relx=0.5, rely=0.5, anchor=tk.CENTER, 
                         width=400, height=300)
        
        # العنوان
        title_label = tk.Label(login_frame, 
                              text="🏆 نظام إدارة معمل الذهب",
                              font=self.fonts['title'],
                              bg=self.colors['white'],
                              fg=self.colors['primary'])
        title_label.pack(pady=20)
        
        subtitle_label = tk.Label(login_frame,
                                 text="Gold Lab Management System",
                                 font=self.fonts['normal'],
                                 bg=self.colors['white'],
                                 fg=self.colors['dark'])
        subtitle_label.pack(pady=(0, 30))
        
        # حقول الإدخال
        tk.Label(login_frame, text="اسم المستخدم:",
                font=self.fonts['normal'], bg=self.colors['white']).pack(pady=5)
        
        self.username_entry = tk.Entry(login_frame, font=self.fonts['normal'],
                                      width=25, justify='center')
        self.username_entry.pack(pady=5)
        
        tk.Label(login_frame, text="كلمة المرور:",
                font=self.fonts['normal'], bg=self.colors['white']).pack(pady=5)
        
        self.password_entry = tk.Entry(login_frame, font=self.fonts['normal'],
                                      width=25, show='*', justify='center')
        self.password_entry.pack(pady=5)
        
        # زر تسجيل الدخول
        login_btn = ttk.Button(login_frame, text="تسجيل الدخول",
                              style='Primary.TButton',
                              command=self.login)
        login_btn.pack(pady=20)
        
        # ربط Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda e: self.login())
        
        # تعيين القيم الافتراضية
        self.username_entry.insert(0, "admin")
        self.password_entry.insert(0, "admin123")
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        try:
            if self.controller.authenticate_user(username, password):
                self.current_user = self.controller.current_user
                messagebox.showinfo("نجح", f"مرحباً {self.current_user['username']}")
                self.show_main_screen()
            else:
                messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تسجيل الدخول: {e}")
    
    def show_main_screen(self):
        """عرض الشاشة الرئيسية"""
        self.clear_window()
        
        # إنشاء القائمة العلوية
        self.create_menu_bar()
        
        # إطار رئيسي
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # الشريط الجانبي
        self.create_sidebar(main_frame)
        
        # المحتوى الرئيسي
        self.content_frame = tk.Frame(main_frame, bg=self.colors['white'],
                                     relief=tk.SUNKEN, bd=1)
        self.content_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # عرض لوحة التحكم افتراضياً
        self.show_dashboard()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="نسخة احتياطية", command=self.backup_database)
        file_menu.add_separator()
        file_menu.add_command(label="تسجيل خروج", command=self.logout)
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة العرض
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="عرض", menu=view_menu)
        view_menu.add_command(label="لوحة التحكم", command=self.show_dashboard)
        view_menu.add_command(label="تحديث", command=self.refresh_current_view)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        sidebar = tk.Frame(parent, bg=self.colors['dark'], width=200)
        sidebar.pack(side=tk.LEFT, fill=tk.Y)
        sidebar.pack_propagate(False)
        
        # عنوان الشريط الجانبي
        title_label = tk.Label(sidebar, text="القوائم الرئيسية",
                              font=self.fonts['header'],
                              bg=self.colors['dark'],
                              fg=self.colors['white'])
        title_label.pack(pady=20)
        
        # أزرار القوائم
        buttons = [
            ("📊 لوحة التحكم", self.show_dashboard),
            ("👥 إدارة العملاء", self.show_customers),
            ("👨‍💼 إدارة الموظفين", self.show_employees),
            ("⚙️ إدارة العمليات", self.show_operations),
            ("💰 إدارة الفواتير", self.show_invoices),
            ("📈 التقارير", self.show_reports),
        ]
        
        for text, command in buttons:
            btn = tk.Button(sidebar, text=text,
                           font=self.fonts['normal'],
                           bg=self.colors['primary'],
                           fg=self.colors['white'],
                           relief=tk.FLAT,
                           width=20,
                           command=command)
            btn.pack(pady=5, padx=10, fill=tk.X)
            
            # تأثير hover
            btn.bind("<Enter>", lambda e, b=btn: b.config(bg=self.colors['secondary']))
            btn.bind("<Leave>", lambda e, b=btn: b.config(bg=self.colors['primary']))
    
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.clear_content()
        
        # عنوان الصفحة
        title_label = tk.Label(self.content_frame, text="📊 لوحة التحكم",
                              font=self.fonts['title'],
                              bg=self.colors['white'])
        title_label.pack(pady=20)
        
        try:
            # الحصول على بيانات لوحة التحكم
            dashboard_data = self.controller.get_dashboard_data()
            
            # إطار الإحصائيات
            stats_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
            stats_frame.pack(fill=tk.X, padx=20, pady=10)
            
            # بطاقات الإحصائيات
            self.create_stat_card(stats_frame, "إجمالي العملاء", 
                                 dashboard_data['total_customers'], 
                                 self.colors['primary'], 0, 0)
            
            self.create_stat_card(stats_frame, "إجمالي الموظفين",
                                 dashboard_data['total_employees'],
                                 self.colors['success'], 0, 1)
            
            pending_samples = len(dashboard_data['pending_operations'].get('pending_samples', []))
            self.create_stat_card(stats_frame, "العينات المعلقة",
                                 pending_samples,
                                 self.colors['warning'], 0, 2)
            
            unpaid_invoices = len(dashboard_data['unpaid_invoices'])
            self.create_stat_card(stats_frame, "الفواتير غير المدفوعة",
                                 unpaid_invoices,
                                 self.colors['danger'], 1, 0)
            
            # حساب إجمالي المبلغ غير المدفوع
            total_unpaid = sum(invoice.get('remaining_amount', 0) 
                             for invoice in dashboard_data['unpaid_invoices'])
            self.create_stat_card(stats_frame, "المبلغ غير المدفوع",
                                 f"{total_unpaid:.2f}",
                                 self.colors['secondary'], 1, 1)
            
            # التقرير اليومي
            today_report = dashboard_data['today_report']
            operations = today_report['operations']
            samples_today = operations.get('samples_added', 0)
            self.create_stat_card(stats_frame, "عينات اليوم",
                                 samples_today,
                                 self.colors['gold'], 1, 2)
            
        except Exception as e:
            error_label = tk.Label(self.content_frame, 
                                  text=f"خطأ في تحميل البيانات: {e}",
                                  font=self.fonts['normal'],
                                  fg=self.colors['danger'],
                                  bg=self.colors['white'])
            error_label.pack(pady=50)
    
    def create_stat_card(self, parent, title, value, color, row, col):
        """إنشاء بطاقة إحصائية"""
        card_frame = tk.Frame(parent, bg=color, relief=tk.RAISED, bd=2)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
        
        # تكوين الشبكة
        parent.grid_columnconfigure(col, weight=1)
        
        value_label = tk.Label(card_frame, text=str(value),
                              font=self.fonts['title'],
                              bg=color, fg=self.colors['white'])
        value_label.pack(pady=(10, 5))
        
        title_label = tk.Label(card_frame, text=title,
                              font=self.fonts['normal'],
                              bg=color, fg=self.colors['white'])
        title_label.pack(pady=(0, 10))
    
    def clear_content(self):
        """مسح المحتوى الحالي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def refresh_current_view(self):
        """تحديث العرض الحالي"""
        # يمكن تحسين هذا لاحقاً لتذكر العرض الحالي
        self.show_dashboard()
    
    def backup_database(self):
        """إنشاء نسخة احتياطية"""
        try:
            success, message = self.controller.backup_database()
            if success:
                messagebox.showinfo("نجح", message)
            else:
                messagebox.showerror("خطأ", message)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية: {e}")
    
    def logout(self):
        """تسجيل الخروج"""
        self.controller.logout_user()
        self.current_user = None
        self.show_login_screen()
    
    def show_customers(self):
        """عرض إدارة العملاء"""
        self.clear_content()

        # عنوان الصفحة
        title_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        title_frame.pack(fill=tk.X, padx=20, pady=10)

        title_label = tk.Label(title_frame, text="👥 إدارة العملاء",
                              font=self.fonts['title'],
                              bg=self.colors['white'])
        title_label.pack(side=tk.LEFT)

        # أزرار العمليات
        btn_frame = tk.Frame(title_frame, bg=self.colors['white'])
        btn_frame.pack(side=tk.RIGHT)

        add_btn = ttk.Button(btn_frame, text="إضافة عميل جديد",
                            style='Success.TButton',
                            command=self.add_customer_dialog)
        add_btn.pack(side=tk.LEFT, padx=5)

        refresh_btn = ttk.Button(btn_frame, text="تحديث",
                               style='Primary.TButton',
                               command=self.show_customers)
        refresh_btn.pack(side=tk.LEFT, padx=5)

        # إطار البحث
        search_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        search_frame.pack(fill=tk.X, padx=20, pady=5)

        tk.Label(search_frame, text="البحث:",
                font=self.fonts['normal'], bg=self.colors['white']).pack(side=tk.LEFT)

        self.customer_search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.customer_search_var,
                               font=self.fonts['normal'], width=30)
        search_entry.pack(side=tk.LEFT, padx=10)

        search_btn = ttk.Button(search_frame, text="بحث",
                               command=self.search_customers)
        search_btn.pack(side=tk.LEFT, padx=5)

        # جدول العملاء
        self.create_customers_table()

        # تحميل البيانات
        self.load_customers_data()

    def create_customers_table(self):
        """إنشاء جدول العملاء"""
        table_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # إنشاء Treeview
        columns = ('ID', 'الاسم', 'رقم الهوية', 'الهاتف', 'العنوان', 'تاريخ الإضافة')
        self.customers_tree = ttk.Treeview(table_frame, columns=columns, show='headings')

        # تعيين عناوين الأعمدة
        for col in columns:
            self.customers_tree.heading(col, text=col)
            self.customers_tree.column(col, width=120, anchor='center')

        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.customers_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.customers_tree.xview)
        self.customers_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # تخطيط الجدول
        self.customers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)

        # ربط النقر المزدوج
        self.customers_tree.bind('<Double-1>', self.edit_customer_dialog)

        # قائمة السياق
        self.create_customer_context_menu()

    def create_customer_context_menu(self):
        """إنشاء قائمة السياق للعملاء"""
        self.customer_context_menu = tk.Menu(self.root, tearoff=0)
        self.customer_context_menu.add_command(label="تعديل", command=self.edit_customer_dialog)
        self.customer_context_menu.add_command(label="حذف", command=self.delete_customer)
        self.customer_context_menu.add_separator()
        self.customer_context_menu.add_command(label="عرض العمليات", command=self.view_customer_operations)

        # ربط النقر بالزر الأيمن
        self.customers_tree.bind('<Button-3>', self.show_customer_context_menu)

    def show_customer_context_menu(self, event):
        """عرض قائمة السياق"""
        try:
            self.customer_context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.customer_context_menu.grab_release()

    def load_customers_data(self):
        """تحميل بيانات العملاء"""
        try:
            # مسح البيانات الحالية
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            # جلب العملاء
            customers = self.controller.customer_model.get_all_customers()

            for customer in customers:
                # تنسيق التاريخ
                created_at = customer.get('created_at', '')
                if created_at:
                    try:
                        date_obj = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                        created_at = date_obj.strftime('%Y-%m-%d')
                    except:
                        pass

                self.customers_tree.insert('', tk.END, values=(
                    customer.get('customer_id', ''),
                    customer.get('name', ''),
                    customer.get('id_number', ''),
                    customer.get('phone', ''),
                    customer.get('address', ''),
                    created_at
                ))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات العملاء: {e}")

    def search_customers(self):
        """البحث في العملاء"""
        search_term = self.customer_search_var.get().strip()

        try:
            # مسح البيانات الحالية
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            if search_term:
                customers = self.controller.customer_model.search_customers(search_term)
            else:
                customers = self.controller.customer_model.get_all_customers()

            for customer in customers:
                created_at = customer.get('created_at', '')
                if created_at:
                    try:
                        date_obj = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                        created_at = date_obj.strftime('%Y-%m-%d')
                    except:
                        pass

                self.customers_tree.insert('', tk.END, values=(
                    customer.get('customer_id', ''),
                    customer.get('name', ''),
                    customer.get('id_number', ''),
                    customer.get('phone', ''),
                    customer.get('address', ''),
                    created_at
                ))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {e}")

    def add_customer_dialog(self):
        """حوار إضافة عميل جديد"""
        dialog = CustomerDialog(self.root, "إضافة عميل جديد")
        if dialog.result:
            try:
                customer_id = self.controller.customer_model.add_customer(
                    dialog.result['name'],
                    dialog.result['id_number'],
                    dialog.result['phone'],
                    dialog.result['address']
                )
                messagebox.showinfo("نجح", f"تم إضافة العميل بنجاح برقم {customer_id}")
                self.load_customers_data()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة العميل: {e}")

    def edit_customer_dialog(self, event=None):
        """حوار تعديل عميل"""
        selected = self.customers_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للتعديل")
            return

        item = self.customers_tree.item(selected[0])
        values = item['values']

        customer_data = {
            'customer_id': values[0],
            'name': values[1],
            'id_number': values[2],
            'phone': values[3],
            'address': values[4]
        }

        dialog = CustomerDialog(self.root, "تعديل العميل", customer_data)
        if dialog.result:
            try:
                success = self.controller.customer_model.update_customer(
                    customer_data['customer_id'],
                    dialog.result
                )
                if success:
                    messagebox.showinfo("نجح", "تم تحديث بيانات العميل بنجاح")
                    self.load_customers_data()
                else:
                    messagebox.showerror("خطأ", "فشل في تحديث بيانات العميل")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحديث العميل: {e}")

    def delete_customer(self):
        """حذف عميل"""
        selected = self.customers_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للحذف")
            return

        item = self.customers_tree.item(selected[0])
        customer_id = item['values'][0]
        customer_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف",
                              f"هل أنت متأكد من حذف العميل '{customer_name}'؟"):
            try:
                success = self.controller.customer_model.delete_customer(customer_id)
                if success:
                    messagebox.showinfo("نجح", "تم حذف العميل بنجاح")
                    self.load_customers_data()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف العميل")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف العميل: {e}")

    def view_customer_operations(self):
        """عرض عمليات العميل"""
        selected = self.customers_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل")
            return

        item = self.customers_tree.item(selected[0])
        customer_id = item['values'][0]
        customer_name = item['values'][1]

        # يمكن تطوير هذا لاحقاً لعرض نافذة منفصلة بعمليات العميل
        messagebox.showinfo("عمليات العميل",
                           f"عرض عمليات العميل: {customer_name}\n"
                           f"رقم العميل: {customer_id}\n\n"
                           "هذه الميزة قيد التطوير...")

    def show_employees(self):
        """عرض إدارة الموظفين"""
        self.clear_content()

        # عنوان الصفحة
        title_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        title_frame.pack(fill=tk.X, padx=20, pady=10)

        title_label = tk.Label(title_frame, text="👨‍💼 إدارة الموظفين",
                              font=self.fonts['title'],
                              bg=self.colors['white'])
        title_label.pack(side=tk.LEFT)

        # أزرار العمليات
        btn_frame = tk.Frame(title_frame, bg=self.colors['white'])
        btn_frame.pack(side=tk.RIGHT)

        add_btn = ttk.Button(btn_frame, text="إضافة موظف جديد",
                            style='Success.TButton',
                            command=self.add_employee_dialog)
        add_btn.pack(side=tk.LEFT, padx=5)

        refresh_btn = ttk.Button(btn_frame, text="تحديث",
                               style='Primary.TButton',
                               command=self.show_employees)
        refresh_btn.pack(side=tk.LEFT, padx=5)

        # إطار البحث
        search_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        search_frame.pack(fill=tk.X, padx=20, pady=5)

        tk.Label(search_frame, text="البحث:",
                font=self.fonts['normal'], bg=self.colors['white']).pack(side=tk.LEFT)

        self.employee_search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.employee_search_var,
                               font=self.fonts['normal'], width=30)
        search_entry.pack(side=tk.LEFT, padx=10)

        search_btn = ttk.Button(search_frame, text="بحث",
                               command=self.search_employees)
        search_btn.pack(side=tk.LEFT, padx=5)

        # جدول الموظفين
        self.create_employees_table()

        # تحميل البيانات
        self.load_employees_data()

    def create_employees_table(self):
        """إنشاء جدول الموظفين"""
        table_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # إنشاء Treeview
        columns = ('ID', 'الاسم', 'رقم الهوية', 'المنصب', 'الهاتف', 'تاريخ الإضافة')
        self.employees_tree = ttk.Treeview(table_frame, columns=columns, show='headings')

        # تعيين عناوين الأعمدة
        for col in columns:
            self.employees_tree.heading(col, text=col)
            self.employees_tree.column(col, width=120, anchor='center')

        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.employees_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.employees_tree.xview)
        self.employees_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # تخطيط الجدول
        self.employees_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)

        # ربط النقر المزدوج
        self.employees_tree.bind('<Double-1>', self.edit_employee_dialog)

        # قائمة السياق
        self.create_employee_context_menu()

    def create_employee_context_menu(self):
        """إنشاء قائمة السياق للموظفين"""
        self.employee_context_menu = tk.Menu(self.root, tearoff=0)
        self.employee_context_menu.add_command(label="تعديل", command=self.edit_employee_dialog)
        self.employee_context_menu.add_command(label="حذف", command=self.delete_employee)
        self.employee_context_menu.add_separator()
        self.employee_context_menu.add_command(label="عرض الأداء", command=self.view_employee_performance)

        # ربط النقر بالزر الأيمن
        self.employees_tree.bind('<Button-3>', self.show_employee_context_menu)

    def show_employee_context_menu(self, event):
        """عرض قائمة السياق للموظفين"""
        try:
            self.employee_context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.employee_context_menu.grab_release()

    def load_employees_data(self):
        """تحميل بيانات الموظفين"""
        try:
            # مسح البيانات الحالية
            for item in self.employees_tree.get_children():
                self.employees_tree.delete(item)

            # جلب الموظفين
            employees = self.controller.employee_model.get_all_employees()

            for employee in employees:
                # تنسيق التاريخ
                created_at = employee.get('created_at', '')
                if created_at:
                    try:
                        date_obj = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                        created_at = date_obj.strftime('%Y-%m-%d')
                    except:
                        pass

                self.employees_tree.insert('', tk.END, values=(
                    employee.get('employee_id', ''),
                    employee.get('name', ''),
                    employee.get('id_number', ''),
                    employee.get('role', ''),
                    employee.get('phone', ''),
                    created_at
                ))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الموظفين: {e}")

    def search_employees(self):
        """البحث في الموظفين"""
        search_term = self.employee_search_var.get().strip()

        try:
            # مسح البيانات الحالية
            for item in self.employees_tree.get_children():
                self.employees_tree.delete(item)

            if search_term:
                employees = self.controller.employee_model.search_employees(search_term)
            else:
                employees = self.controller.employee_model.get_all_employees()

            for employee in employees:
                created_at = employee.get('created_at', '')
                if created_at:
                    try:
                        date_obj = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                        created_at = date_obj.strftime('%Y-%m-%d')
                    except:
                        pass

                self.employees_tree.insert('', tk.END, values=(
                    employee.get('employee_id', ''),
                    employee.get('name', ''),
                    employee.get('id_number', ''),
                    employee.get('role', ''),
                    employee.get('phone', ''),
                    created_at
                ))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {e}")

    def add_employee_dialog(self):
        """حوار إضافة موظف جديد"""
        dialog = EmployeeDialog(self.root, "إضافة موظف جديد")
        if dialog.result:
            try:
                employee_id = self.controller.employee_model.add_employee(
                    dialog.result['name'],
                    dialog.result['id_number'],
                    dialog.result['role'],
                    dialog.result['phone']
                )
                messagebox.showinfo("نجح", f"تم إضافة الموظف بنجاح برقم {employee_id}")
                self.load_employees_data()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة الموظف: {e}")

    def edit_employee_dialog(self, event=None):
        """حوار تعديل موظف"""
        selected = self.employees_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار موظف للتعديل")
            return

        item = self.employees_tree.item(selected[0])
        values = item['values']

        employee_data = {
            'employee_id': values[0],
            'name': values[1],
            'id_number': values[2],
            'role': values[3],
            'phone': values[4]
        }

        dialog = EmployeeDialog(self.root, "تعديل الموظف", employee_data)
        if dialog.result:
            try:
                success = self.controller.employee_model.update_employee(
                    employee_data['employee_id'],
                    dialog.result
                )
                if success:
                    messagebox.showinfo("نجح", "تم تحديث بيانات الموظف بنجاح")
                    self.load_employees_data()
                else:
                    messagebox.showerror("خطأ", "فشل في تحديث بيانات الموظف")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحديث الموظف: {e}")

    def delete_employee(self):
        """حذف موظف"""
        selected = self.employees_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار موظف للحذف")
            return

        item = self.employees_tree.item(selected[0])
        employee_id = item['values'][0]
        employee_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف",
                              f"هل أنت متأكد من حذف الموظف '{employee_name}'؟"):
            try:
                success = self.controller.employee_model.delete_employee(employee_id)
                if success:
                    messagebox.showinfo("نجح", "تم حذف الموظف بنجاح")
                    self.load_employees_data()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف الموظف")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف الموظف: {e}")

    def view_employee_performance(self):
        """عرض أداء الموظف"""
        selected = self.employees_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار موظف")
            return

        item = self.employees_tree.item(selected[0])
        employee_id = item['values'][0]
        employee_name = item['values'][1]

        # يمكن تطوير هذا لاحقاً لعرض نافذة منفصلة بأداء الموظف
        messagebox.showinfo("أداء الموظف",
                           f"عرض أداء الموظف: {employee_name}\n"
                           f"رقم الموظف: {employee_id}\n\n"
                           "هذه الميزة قيد التطوير...")

    def show_operations(self):
        """عرض إدارة العمليات"""
        self.clear_content()

        title_label = tk.Label(self.content_frame, text="⚙️ إدارة العمليات",
                              font=self.fonts['title'],
                              bg=self.colors['white'])
        title_label.pack(pady=20)

        # إطار الأزرار
        buttons_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        # أزرار العمليات
        operations_buttons = [
            ("إضافة عينة للتحليل", self.add_sample_analysis, "#007BFF"),
            ("إضافة عملية تنقية", self.add_refinement_process, "#28A745"),
            ("إضافة عملية صب", self.add_casting_process, "#FD7E14"),
            ("العمليات المعلقة", self.show_pending_operations, "#6F42C1")
        ]

        for i, (text, command, color) in enumerate(operations_buttons):
            btn = tk.Button(buttons_frame, text=text, font=self.fonts['button'],
                           bg=color, fg="white", command=command, width=20, height=2)
            btn.grid(row=i//2, column=i%2, padx=10, pady=5, sticky="ew")

        # تكوين الأعمدة
        buttons_frame.grid_columnconfigure(0, weight=1)
        buttons_frame.grid_columnconfigure(1, weight=1)

        # إطار العمليات الحديثة
        recent_frame = tk.LabelFrame(self.content_frame, text="العمليات الحديثة",
                                    font=self.fonts['normal'], bg=self.colors['white'])
        recent_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # جدول العمليات الحديثة
        self.create_recent_operations_table(recent_frame)

    def show_invoices(self):
        """عرض إدارة الفواتير"""
        self.clear_content()

        title_label = tk.Label(self.content_frame, text="💰 إدارة الفواتير",
                              font=self.fonts['title'],
                              bg=self.colors['white'])
        title_label.pack(pady=20)

        # إطار الأزرار
        buttons_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        # أزرار الفواتير
        invoice_buttons = [
            ("إنشاء فاتورة تحليل", self.create_sample_invoice, "#007BFF"),
            ("إنشاء فاتورة صب", self.create_casting_invoice, "#28A745"),
            ("إضافة دفعة", self.add_payment_to_invoice, "#FD7E14"),
            ("الفواتير غير المدفوعة", self.show_unpaid_invoices, "#DC3545")
        ]

        for i, (text, command, color) in enumerate(invoice_buttons):
            btn = tk.Button(buttons_frame, text=text, font=self.fonts['button'],
                           bg=color, fg="white", command=command, width=20, height=2)
            btn.grid(row=i//2, column=i%2, padx=10, pady=5, sticky="ew")

        # تكوين الأعمدة
        buttons_frame.grid_columnconfigure(0, weight=1)
        buttons_frame.grid_columnconfigure(1, weight=1)

        # إطار الفواتير الحديثة
        recent_frame = tk.LabelFrame(self.content_frame, text="الفواتير الحديثة",
                                    font=self.fonts['normal'], bg=self.colors['white'])
        recent_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # جدول الفواتير الحديثة
        self.create_recent_invoices_table(recent_frame)

    def show_reports(self):
        """عرض التقارير"""
        self.clear_content()

        title_label = tk.Label(self.content_frame, text="📈 التقارير",
                              font=self.fonts['title'],
                              bg=self.colors['white'])
        title_label.pack(pady=20)

        # إطار الأزرار
        buttons_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        # أزرار التقارير
        report_buttons = [
            ("التقرير اليومي", self.show_daily_report, "#007BFF"),
            ("التقرير الأسبوعي", self.show_weekly_report, "#28A745"),
            ("التقرير الشهري", self.show_monthly_report, "#FD7E14"),
            ("تقرير الأداء", self.show_performance_report, "#6F42C1")
        ]

        for i, (text, command, color) in enumerate(report_buttons):
            btn = tk.Button(buttons_frame, text=text, font=self.fonts['button'],
                           bg=color, fg="white", command=command, width=20, height=2)
            btn.grid(row=i//2, column=i%2, padx=10, pady=5, sticky="ew")

        # تكوين الأعمدة
        buttons_frame.grid_columnconfigure(0, weight=1)
        buttons_frame.grid_columnconfigure(1, weight=1)

        # إطار ملخص سريع
        summary_frame = tk.LabelFrame(self.content_frame, text="ملخص سريع",
                                     font=self.fonts['normal'], bg=self.colors['white'])
        summary_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # عرض ملخص سريع
        self.create_quick_summary(summary_frame)

    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
نظام إدارة معمل الذهب
Gold Lab Management System

الإصدار: 1.0.0
تطوير: فريق التطوير

نظام شامل لإدارة معامل الذهب والمعادن النفيسة
يوفر إدارة العملاء والموظفين والعمليات والفواتير والتقارير

© 2025 جميع الحقوق محفوظة
        """
        messagebox.showinfo("حول البرنامج", about_text)

    # ==================== طرق العمليات ====================

    def add_sample_analysis(self):
        """إضافة عينة للتحليل"""
        try:
            customers = self.controller.customer_model.get_all_customers()
            employees = self.controller.employee_model.get_all_employees()

            dialog = SampleAnalysisDialog(self.root, "إضافة عينة للتحليل", customers, employees)

            if dialog.result:
                success, message, sample_id = self.controller.add_sample_analysis(
                    dialog.result['customer_id'],
                    dialog.result['weight'],
                    dialog.result['material_type'],
                    dialog.result['employee_id']
                )

                if success:
                    messagebox.showinfo("نجح", message)
                    self.refresh_dashboard()
                else:
                    messagebox.showerror("خطأ", message)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة العينة: {e}")

    def add_refinement_process(self):
        """إضافة عملية تنقية"""
        try:
            # الحصول على العينات المكتملة
            pending_ops = self.controller.get_pending_operations()
            completed_samples = pending_ops.get('completed_samples', [])

            if not completed_samples:
                messagebox.showwarning("تحذير", "لا توجد عينات مكتملة للتنقية")
                return

            employees = self.controller.employee_model.get_all_employees()

            dialog = RefinementDialog(self.root, "إضافة عملية تنقية", completed_samples, employees)

            if dialog.result:
                success, message, refinement_id = self.controller.add_refinement_process(
                    dialog.result['sample_id'],
                    dialog.result['raw_weight'],
                    dialog.result['chemicals_used'],
                    dialog.result['employee_id']
                )

                if success:
                    messagebox.showinfo("نجح", message)
                    self.refresh_dashboard()
                else:
                    messagebox.showerror("خطأ", message)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة عملية التنقية: {e}")

    def add_casting_process(self):
        """إضافة عملية صب"""
        try:
            customers = self.controller.customer_model.get_all_customers()
            employees = self.controller.employee_model.get_all_employees()

            # الحصول على عمليات التنقية المكتملة
            pending_ops = self.controller.get_pending_operations()
            completed_refinements = pending_ops.get('completed_refinements', [])

            dialog = CastingDialog(self.root, "إضافة عملية صب", customers, employees, completed_refinements)

            if dialog.result:
                success, message, casting_id = self.controller.add_casting_process(
                    dialog.result['customer_id'],
                    dialog.result['bar_weight'],
                    dialog.result['bar_shape'],
                    dialog.result['purity_percentage'],
                    dialog.result['refinement_id'],
                    dialog.result['employee_id']
                )

                if success:
                    messagebox.showinfo("نجح", message)
                    self.refresh_dashboard()
                else:
                    messagebox.showerror("خطأ", message)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة عملية الصب: {e}")

    def show_pending_operations(self):
        """عرض العمليات المعلقة"""
        try:
            pending_ops = self.controller.get_pending_operations()

            # إنشاء نافذة جديدة
            pending_window = tk.Toplevel(self.root)
            pending_window.title("العمليات المعلقة")
            pending_window.geometry("800x600")
            pending_window.transient(self.root)

            # عنوان
            title_label = tk.Label(pending_window, text="العمليات المعلقة",
                                  font=self.fonts['title'])
            title_label.pack(pady=20)

            # إطار للمحتوى
            content_frame = tk.Frame(pending_window)
            content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

            # العينات المعلقة
            samples_frame = tk.LabelFrame(content_frame, text="العينات المعلقة", font=self.fonts['normal'])
            samples_frame.pack(fill=tk.BOTH, expand=True, pady=5)

            pending_samples = pending_ops.get('pending_samples', [])
            if pending_samples:
                for sample in pending_samples:
                    sample_text = f"عينة #{sample['sample_id']} - {sample['customer_name']} ({sample['weight']}g)"
                    tk.Label(samples_frame, text=sample_text, font=self.fonts['normal']).pack(anchor=tk.W, padx=10, pady=2)
            else:
                tk.Label(samples_frame, text="لا توجد عينات معلقة", font=self.fonts['normal']).pack(pady=10)

            # عمليات التنقية المعلقة
            refinements_frame = tk.LabelFrame(content_frame, text="عمليات التنقية المعلقة", font=self.fonts['normal'])
            refinements_frame.pack(fill=tk.BOTH, expand=True, pady=5)

            pending_refinements = pending_ops.get('pending_refinements', [])
            if pending_refinements:
                for refinement in pending_refinements:
                    refinement_text = f"تنقية #{refinement['refinement_id']} - {refinement['customer_name']} ({refinement['raw_weight']}g)"
                    tk.Label(refinements_frame, text=refinement_text, font=self.fonts['normal']).pack(anchor=tk.W, padx=10, pady=2)
            else:
                tk.Label(refinements_frame, text="لا توجد عمليات تنقية معلقة", font=self.fonts['normal']).pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض العمليات المعلقة: {e}")

    def create_recent_operations_table(self, parent):
        """إنشاء جدول العمليات الحديثة"""
        # إطار الجدول
        table_frame = tk.Frame(parent, bg=self.colors['white'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # عنوان مؤقت
        temp_label = tk.Label(table_frame, text="جدول العمليات الحديثة قيد التطوير...",
                             font=self.fonts['normal'], bg=self.colors['white'])
        temp_label.pack(pady=20)

    # ==================== طرق الفواتير ====================

    def create_sample_invoice(self):
        """إنشاء فاتورة تحليل عينة"""
        try:
            # الحصول على العينات المكتملة بدون فواتير
            pending_ops = self.controller.get_pending_operations()
            completed_samples = pending_ops.get('completed_samples', [])

            if not completed_samples:
                messagebox.showwarning("تحذير", "لا توجد عينات مكتملة لإنشاء فواتير لها")
                return

            dialog = InvoiceDialog(self.root, "إنشاء فاتورة تحليل", "sample", samples_list=completed_samples)

            if dialog.result:
                success, message, invoice_id = self.controller.create_sample_invoice(
                    dialog.result['sample_id'],
                    dialog.result['paid_amount']
                )

                if success:
                    messagebox.showinfo("نجح", message)
                    self.refresh_dashboard()
                else:
                    messagebox.showerror("خطأ", message)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء الفاتورة: {e}")

    def create_casting_invoice(self):
        """إنشاء فاتورة عملية صب"""
        try:
            # الحصول على عمليات الصب المكتملة بدون فواتير
            messagebox.showinfo("قيد التطوير", "إنشاء فواتير الصب قيد التطوير...")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء فاتورة الصب: {e}")

    def add_payment_to_invoice(self):
        """إضافة دفعة لفاتورة"""
        try:
            # الحصول على الفواتير غير المدفوعة
            unpaid_invoices = self.controller.get_unpaid_invoices()

            if not unpaid_invoices:
                messagebox.showwarning("تحذير", "لا توجد فواتير غير مدفوعة")
                return

            # إنشاء نافذة اختيار الفاتورة
            invoice_window = tk.Toplevel(self.root)
            invoice_window.title("اختيار الفاتورة")
            invoice_window.geometry("600x400")
            invoice_window.transient(self.root)
            invoice_window.grab_set()

            # قائمة الفواتير
            tk.Label(invoice_window, text="اختر الفاتورة لإضافة دفعة:",
                    font=self.fonts['normal']).pack(pady=10)

            invoice_listbox = tk.Listbox(invoice_window, font=self.fonts['normal'], height=10)
            invoice_listbox.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

            for invoice in unpaid_invoices:
                remaining = invoice['total_amount'] - invoice['paid_amount']
                invoice_text = f"فاتورة #{invoice['invoice_id']} - {invoice['customer_name']} - متبقي: {remaining:.2f}"
                invoice_listbox.insert(tk.END, invoice_text)

            def add_payment():
                selection = invoice_listbox.curselection()
                if not selection:
                    messagebox.showwarning("تحذير", "يرجى اختيار فاتورة")
                    return

                selected_invoice = unpaid_invoices[selection[0]]
                invoice_window.destroy()

                # فتح حوار إضافة الدفعة
                dialog = InvoiceDialog(self.root, "إضافة دفعة", "payment", invoice_data=selected_invoice)

                if dialog.result:
                    success, message = self.controller.add_payment_to_invoice(
                        dialog.result['invoice_id'],
                        dialog.result['paid_amount']
                    )

                    if success:
                        messagebox.showinfo("نجح", message)
                        self.refresh_dashboard()
                    else:
                        messagebox.showerror("خطأ", message)

            tk.Button(invoice_window, text="إضافة دفعة", command=add_payment,
                     font=self.fonts['button'], bg="#28A745", fg="white").pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة الدفعة: {e}")

    def show_unpaid_invoices(self):
        """عرض الفواتير غير المدفوعة"""
        try:
            unpaid_invoices = self.controller.get_unpaid_invoices()

            # إنشاء نافذة جديدة
            unpaid_window = tk.Toplevel(self.root)
            unpaid_window.title("الفواتير غير المدفوعة")
            unpaid_window.geometry("800x600")
            unpaid_window.transient(self.root)

            # عنوان
            title_label = tk.Label(unpaid_window, text="الفواتير غير المدفوعة",
                                  font=self.fonts['title'])
            title_label.pack(pady=20)

            if unpaid_invoices:
                # إطار الجدول
                table_frame = tk.Frame(unpaid_window)
                table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

                # عناوين الأعمدة
                headers = ["رقم الفاتورة", "العميل", "إجمالي المبلغ", "المبلغ المدفوع", "المبلغ المتبقي"]
                for i, header in enumerate(headers):
                    tk.Label(table_frame, text=header, font=self.fonts['button'],
                            relief=tk.RIDGE, width=15).grid(row=0, column=i, sticky="ew")

                # بيانات الفواتير
                for i, invoice in enumerate(unpaid_invoices, 1):
                    remaining = invoice['total_amount'] - invoice['paid_amount']
                    values = [
                        invoice['invoice_id'],
                        invoice['customer_name'],
                        f"{invoice['total_amount']:.2f}",
                        f"{invoice['paid_amount']:.2f}",
                        f"{remaining:.2f}"
                    ]

                    for j, value in enumerate(values):
                        tk.Label(table_frame, text=str(value), font=self.fonts['normal'],
                                relief=tk.RIDGE, width=15).grid(row=i, column=j, sticky="ew")
            else:
                tk.Label(unpaid_window, text="لا توجد فواتير غير مدفوعة",
                        font=self.fonts['normal']).pack(pady=50)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض الفواتير غير المدفوعة: {e}")

    def create_recent_invoices_table(self, parent):
        """إنشاء جدول الفواتير الحديثة"""
        # إطار الجدول
        table_frame = tk.Frame(parent, bg=self.colors['white'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # عنوان مؤقت
        temp_label = tk.Label(table_frame, text="جدول الفواتير الحديثة قيد التطوير...",
                             font=self.fonts['normal'], bg=self.colors['white'])
        temp_label.pack(pady=20)

    # ==================== طرق التقارير ====================

    def show_daily_report(self):
        """عرض التقرير اليومي"""
        try:
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')

            report_data = self.controller.get_daily_report(today)

            # إنشاء نافذة التقرير
            report_window = tk.Toplevel(self.root)
            report_window.title(f"التقرير اليومي - {today}")
            report_window.geometry("700x500")
            report_window.transient(self.root)

            # عنوان
            title_label = tk.Label(report_window, text=f"التقرير اليومي - {today}",
                                  font=self.fonts['title'])
            title_label.pack(pady=20)

            # إطار المحتوى
            content_frame = tk.Frame(report_window)
            content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

            # ملخص العمليات
            operations_frame = tk.LabelFrame(content_frame, text="ملخص العمليات", font=self.fonts['normal'])
            operations_frame.pack(fill=tk.X, pady=5)

            tk.Label(operations_frame, text=f"عدد العينات الجديدة: {report_data.get('new_samples', 0)}",
                    font=self.fonts['normal']).pack(anchor=tk.W, padx=10, pady=2)
            tk.Label(operations_frame, text=f"عدد العينات المكتملة: {report_data.get('completed_samples', 0)}",
                    font=self.fonts['normal']).pack(anchor=tk.W, padx=10, pady=2)
            tk.Label(operations_frame, text=f"عدد عمليات التنقية: {report_data.get('refinements', 0)}",
                    font=self.fonts['normal']).pack(anchor=tk.W, padx=10, pady=2)
            tk.Label(operations_frame, text=f"عدد عمليات الصب: {report_data.get('castings', 0)}",
                    font=self.fonts['normal']).pack(anchor=tk.W, padx=10, pady=2)

            # ملخص مالي
            financial_frame = tk.LabelFrame(content_frame, text="ملخص مالي", font=self.fonts['normal'])
            financial_frame.pack(fill=tk.X, pady=5)

            tk.Label(financial_frame, text=f"إجمالي الإيرادات: {report_data.get('total_revenue', 0):.2f}",
                    font=self.fonts['normal']).pack(anchor=tk.W, padx=10, pady=2)
            tk.Label(financial_frame, text=f"المبالغ المحصلة: {report_data.get('collected_amount', 0):.2f}",
                    font=self.fonts['normal']).pack(anchor=tk.W, padx=10, pady=2)
            tk.Label(financial_frame, text=f"المبالغ المعلقة: {report_data.get('pending_amount', 0):.2f}",
                    font=self.fonts['normal']).pack(anchor=tk.W, padx=10, pady=2)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض التقرير اليومي: {e}")

    def show_weekly_report(self):
        """عرض التقرير الأسبوعي"""
        try:
            from datetime import datetime, timedelta
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)

            report_data = self.controller.get_weekly_report(
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )

            # إنشاء نافذة التقرير
            report_window = tk.Toplevel(self.root)
            report_window.title("التقرير الأسبوعي")
            report_window.geometry("700x500")
            report_window.transient(self.root)

            # عنوان
            title_label = tk.Label(report_window, text="التقرير الأسبوعي",
                                  font=self.fonts['title'])
            title_label.pack(pady=20)

            # عرض البيانات (مشابه للتقرير اليومي)
            content_frame = tk.Frame(report_window)
            content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

            tk.Label(content_frame, text="التقرير الأسبوعي قيد التطوير...",
                    font=self.fonts['normal']).pack(pady=50)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض التقرير الأسبوعي: {e}")

    def show_monthly_report(self):
        """عرض التقرير الشهري"""
        try:
            from datetime import datetime
            current_month = datetime.now().strftime('%Y-%m')

            report_data = self.controller.get_monthly_report(current_month)

            # إنشاء نافذة التقرير
            report_window = tk.Toplevel(self.root)
            report_window.title(f"التقرير الشهري - {current_month}")
            report_window.geometry("700x500")
            report_window.transient(self.root)

            # عنوان
            title_label = tk.Label(report_window, text=f"التقرير الشهري - {current_month}",
                                  font=self.fonts['title'])
            title_label.pack(pady=20)

            # عرض البيانات
            content_frame = tk.Frame(report_window)
            content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

            tk.Label(content_frame, text="التقرير الشهري قيد التطوير...",
                    font=self.fonts['normal']).pack(pady=50)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض التقرير الشهري: {e}")

    def show_performance_report(self):
        """عرض تقرير الأداء"""
        try:
            # إنشاء نافذة التقرير
            report_window = tk.Toplevel(self.root)
            report_window.title("تقرير الأداء")
            report_window.geometry("700x500")
            report_window.transient(self.root)

            # عنوان
            title_label = tk.Label(report_window, text="تقرير الأداء",
                                  font=self.fonts['title'])
            title_label.pack(pady=20)

            # عرض البيانات
            content_frame = tk.Frame(report_window)
            content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

            tk.Label(content_frame, text="تقرير الأداء قيد التطوير...",
                    font=self.fonts['normal']).pack(pady=50)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض تقرير الأداء: {e}")

    def create_quick_summary(self, parent):
        """إنشاء ملخص سريع"""
        # إطار الملخص
        summary_frame = tk.Frame(parent, bg=self.colors['white'])
        summary_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # عنوان مؤقت
        temp_label = tk.Label(summary_frame, text="الملخص السريع قيد التطوير...",
                             font=self.fonts['normal'], bg=self.colors['white'])
        temp_label.pack(pady=20)

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        app = GoldLabDesktopApp()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
