import React from 'react';

const Sidebar = ({ isOpen, activeSection, onSectionChange }) => {
  const menuItems = [
    {
      id: 'dashboard',
      label: 'لوحة التحكم',
      icon: '📊'
    },
    {
      id: 'customers',
      label: 'إدارة العملاء',
      icon: '👥'
    },
    {
      id: 'employees',
      label: 'إدارة الموظفين',
      icon: '👨‍💼'
    },
    {
      id: 'operations',
      label: 'إدارة العمليات',
      icon: '⚗️'
    },
    {
      id: 'invoices',
      label: 'إدارة الفواتير',
      icon: '🧾'
    },
    {
      id: 'reports',
      label: 'التقارير',
      icon: '📈'
    }
  ];

  return (
    <div className={`sidebar ${isOpen ? 'open' : 'closed'}`}>
      <div className="sidebar-header">
        <h2>🏆 معمل الذهب</h2>
        {isOpen && <p>نظام الإدارة المتكامل</p>}
      </div>
      
      <nav className="sidebar-nav">
        {menuItems.map((item) => (
          <button
            key={item.id}
            className={`nav-item ${activeSection === item.id ? 'active' : ''}`}
            onClick={() => onSectionChange(item.id)}
          >
            <span className="nav-item-icon">{item.icon}</span>
            <span className="nav-item-text">{item.label}</span>
          </button>
        ))}
      </nav>
    </div>
  );
};

export default Sidebar;

