# دليل المستخدم الشامل - نظام إدارة معمل الذهب

## المحتويات

1. [مقدمة عن النظام](#مقدمة-عن-النظام)
2. [متطلبات التشغيل](#متطلبات-التشغيل)
3. [تسجيل الدخول](#تسجيل-الدخول)
4. [لوحة التحكم الرئيسية](#لوحة-التحكم-الرئيسية)
5. [إدارة العملاء](#إدارة-العملاء)
6. [إدارة الموظفين](#إدارة-الموظفين)
7. [إدارة العمليات](#إدارة-العمليات)
8. [إدارة الفواتير](#إدارة-الفواتير)
9. [التقارير والإحصائيات](#التقارير-والإحصائيات)
10. [الإعدادات والصيانة](#الإعدادات-والصيانة)
11. [استكشاف الأخطاء وإصلاحها](#استكشاف-الأخطاء-وإصلاحها)

---

## مقدمة عن النظام

نظام إدارة معمل الذهب هو حل شامل ومتكامل تم تطويره خصيصاً لإدارة جميع عمليات معامل الذهب والمعادن النفيسة. يوفر النظام واجهة عربية سهلة الاستخدام مع إمكانيات متقدمة لإدارة العملاء والموظفين والعمليات والفواتير والتقارير.

### الميزات الرئيسية

**إدارة شاملة للعملاء:**
يتيح النظام إدارة قاعدة بيانات شاملة للعملاء تتضمن المعلومات الشخصية وتاريخ التعاملات والعمليات المنجزة. يمكن للمستخدمين إضافة عملاء جدد وتعديل بياناتهم وتتبع تاريخ تعاملاتهم مع المعمل.

**إدارة الموظفين وتقييم الأداء:**
يوفر النظام نظاماً متكاملاً لإدارة الموظفين يشمل تسجيل بياناتهم الشخصية وتخصصاتهم وتتبع أدائهم في العمليات المختلفة. كما يتضمن نظام تقييم الأداء الذي يساعد في مراقبة كفاءة الموظفين وتحسين الإنتاجية.

**إدارة العمليات التقنية:**
يغطي النظام جميع العمليات التقنية في معمل الذهب بما في ذلك تحليل العينات وعمليات التنقية وصب السبائك. كل عملية لها نموذج مخصص يتضمن جميع البيانات التقنية المطلوبة مثل الأوزان ونسب النقاء والمواد المستخدمة.

**نظام فوترة متقدم:**
يتضمن النظام نظام فوترة شامل يدعم إنشاء الفواتير وتتبع المدفوعات والمبالغ المستحقة. يمكن للنظام التعامل مع الدفعات الجزئية وتتبع حالة كل فاتورة.

**تقارير وإحصائيات مفصلة:**
يوفر النظام مجموعة شاملة من التقارير اليومية والأسبوعية والشهرية والمخصصة. تشمل التقارير إحصائيات العمليات والأداء المالي وأداء الموظفين وأهم العملاء.

### التقنيات المستخدمة

تم تطوير النظام باستخدام أحدث التقنيات لضمان الأداء العالي والأمان:

- **الواجهة الأمامية:** React.js مع تصميم متجاوب يدعم جميع الأجهزة
- **قاعدة البيانات:** SQLite لضمان الموثوقية وسهولة الصيانة
- **الأمان:** نظام تشفير متقدم لحماية البيانات الحساسة
- **التصميم:** واجهة عربية حديثة وسهلة الاستخدام

---

## متطلبات التشغيل

### متطلبات الأجهزة

**الحد الأدنى:**
- معالج: Intel Core i3 أو AMD Ryzen 3
- الذاكرة: 4 جيجابايت RAM
- مساحة التخزين: 2 جيجابايت مساحة فارغة
- الشاشة: دقة 1024x768 بكسل

**المتطلبات الموصى بها:**
- معالج: Intel Core i5 أو AMD Ryzen 5 أو أحدث
- الذاكرة: 8 جيجابايت RAM أو أكثر
- مساحة التخزين: 10 جيجابايت مساحة فارغة
- الشاشة: دقة 1920x1080 بكسل أو أعلى

### متطلبات البرمجيات

**أنظمة التشغيل المدعومة:**
- Windows 10 أو أحدث
- macOS 10.14 أو أحدث
- Ubuntu 18.04 أو أحدث
- أي توزيعة Linux حديثة

**متصفحات الويب المدعومة:**
- Google Chrome 90 أو أحدث
- Mozilla Firefox 88 أو أحدث
- Microsoft Edge 90 أو أحدث
- Safari 14 أو أحدث

### متطلبات الشبكة

- اتصال إنترنت مستقر (للتحديثات والنسخ الاحتياطي)
- سرعة تحميل: 10 ميجابت/ثانية كحد أدنى
- سرعة رفع: 5 ميجابت/ثانية كحد أدنى

---

## تسجيل الدخول

### الوصول إلى النظام

عند فتح النظام لأول مرة، ستظهر شاشة تسجيل الدخول التي تتضمن حقلين رئيسيين:

1. **اسم المستخدم:** أدخل اسم المستخدم الخاص بك
2. **كلمة المرور:** أدخل كلمة المرور الخاصة بك

### بيانات الدخول الافتراضية

للاختبار والتجربة، يمكن استخدام البيانات التالية:
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

> **تنبيه أمني:** يجب تغيير كلمة المرور الافتراضية فور تسجيل الدخول لأول مرة لضمان أمان النظام.

### إجراءات الأمان

النظام يتضمن عدة طبقات أمان لحماية البيانات:

**تشفير كلمات المرور:**
جميع كلمات المرور محفوظة بتشفير متقدم ولا يمكن الوصول إليها حتى من قبل مطوري النظام.

**جلسات آمنة:**
يستخدم النظام تقنية الجلسات الآمنة التي تنتهي تلقائياً بعد فترة عدم نشاط محددة.

**تسجيل العمليات:**
جميع العمليات الحساسة يتم تسجيلها مع الوقت والتاريخ واسم المستخدم لأغراض المراجعة والتدقيق.

### استعادة كلمة المرور

في حالة نسيان كلمة المرور، يمكن التواصل مع مدير النظام لإعادة تعيين كلمة مرور جديدة. يتطلب هذا الإجراء التحقق من الهوية لضمان الأمان.

---

## لوحة التحكم الرئيسية

لوحة التحكم هي النقطة المركزية في النظام وتوفر نظرة شاملة على جميع أنشطة المعمل. تتضمن اللوحة عدة أقسام رئيسية تعرض المعلومات الأكثر أهمية بشكل مرئي وسهل الفهم.

### الإحصائيات اليومية

تعرض لوحة التحكم أربع بطاقات إحصائية رئيسية تلخص أنشطة اليوم:

**عينات محللة اليوم:**
تظهر العدد الإجمالي للعينات التي تم تحليلها في اليوم الحالي. هذا المؤشر مهم لمراقبة حجم العمل اليومي وقياس الإنتاجية.

**سبائك مصبوبة اليوم:**
تعرض عدد السبائك التي تم صبها في اليوم الحالي. يساعد هذا المؤشر في تتبع الإنتاج اليومي من السبائك.

**إيرادات اليوم:**
تظهر إجمالي الإيرادات المحققة في اليوم الحالي بالريال السعودي. يتم حساب هذا المبلغ من جميع الفواتير المدفوعة في نفس اليوم.

**عمليات معلقة:**
تعرض عدد العمليات التي لا تزال قيد التنفيذ أو في انتظار الإكمال. هذا المؤشر مهم لإدارة سير العمل وتحديد الأولويات.

### الأنشطة الحديثة

يعرض هذا القسم قائمة بأحدث الأنشطة التي تمت في النظام مرتبة حسب الوقت. كل نشاط يتضمن:

- **نوع النشاط:** تحليل عينة، صب سبيكة، إنشاء فاتورة، إلخ
- **وصف مفصل:** تفاصيل العملية والعميل المرتبط بها
- **الوقت:** وقت تنفيذ العملية
- **الحالة:** مؤشر لوني يوضح حالة العملية (مكتمل، قيد التنفيذ، معلق)

### الفواتير المعلقة

يعرض هذا القسم قائمة بالفواتير التي لم يتم دفعها بالكامل أو التي تجاوزت تاريخ الاستحقاق. لكل فاتورة معلقة يتم عرض:

- **اسم العميل**
- **المبلغ المستحق**
- **تاريخ الاستحقاق**
- **عدد الأيام المتأخرة (إن وجدت)**

### أهم العملاء

يعرض هذا الجدول قائمة بأهم العملاء من حيث عدد العمليات وإجمالي الإيرادات. يساعد هذا القسم في:

- تحديد العملاء الأكثر قيمة للمعمل
- تخصيص خدمة عملاء مميزة للعملاء المهمين
- تحليل أنماط التعامل مع العملاء المختلفين

### التنقل في النظام

الشريط الجانبي يحتوي على جميع الأقسام الرئيسية للنظام:

- **لوحة التحكم:** العودة إلى الصفحة الرئيسية
- **إدارة العملاء:** إضافة وتعديل بيانات العملاء
- **إدارة الموظفين:** إدارة بيانات الموظفين وتقييم الأداء
- **إدارة العمليات:** تسجيل ومتابعة العمليات التقنية
- **إدارة الفواتير:** إنشاء ومتابعة الفواتير والمدفوعات
- **التقارير:** إنشاء وعرض التقارير المختلفة

### الرأس العلوي

يحتوي الرأس العلوي على:

- **زر القائمة:** لإخفاء أو إظهار الشريط الجانبي
- **التاريخ والوقت الحالي:** يتم تحديثه تلقائياً
- **معلومات المستخدم:** اسم المستخدم الحالي ودوره في النظام
- **زر تسجيل الخروج:** للخروج الآمن من النظام




---

## إدارة العملاء

قسم إدارة العملاء هو أحد الأقسام الأساسية في النظام ويوفر إمكانيات شاملة لإدارة قاعدة بيانات العملاء. يتيح هذا القسم للمستخدمين إضافة عملاء جدد وتعديل بياناتهم الموجودة وحذف العملاء غير النشطين والبحث في قاعدة البيانات.

### إضافة عميل جديد

لإضافة عميل جديد، اتبع الخطوات التالية:

1. **الانتقال إلى قسم إدارة العملاء:** انقر على "إدارة العملاء" في الشريط الجانبي
2. **النقر على زر الإضافة:** انقر على زر "إضافة عميل جديد" في أعلى الصفحة
3. **ملء البيانات المطلوبة:**

**البيانات الأساسية:**
- **اسم العميل (مطلوب):** أدخل الاسم الكامل للعميل كما هو مسجل في الهوية
- **رقم الهوية (مطلوب):** أدخل رقم الهوية الوطنية أو الإقامة (10 أرقام)
- **رقم الهاتف:** أدخل رقم الهاتف المحمول للتواصل
- **العنوان:** أدخل العنوان التفصيلي للعميل

4. **حفظ البيانات:** انقر على زر "إضافة العميل" لحفظ البيانات

### تعديل بيانات العميل

يمكن تعديل بيانات أي عميل موجود من خلال:

1. **البحث عن العميل:** استخدم خاصية البحث للعثور على العميل المطلوب
2. **النقر على زر التعديل:** انقر على زر "تعديل" المقابل لاسم العميل
3. **تعديل البيانات:** قم بتعديل البيانات المطلوبة في النموذج
4. **حفظ التغييرات:** انقر على زر "حفظ التغييرات"

### البحث والفلترة

النظام يوفر إمكانيات بحث متقدمة تشمل:

**البحث النصي:**
يمكن البحث باستخدام أي من المعايير التالية:
- اسم العميل (البحث الجزئي مدعوم)
- رقم الهوية
- رقم الهاتف

**الفلترة حسب النشاط:**
يمكن فلترة العملاء حسب مستوى نشاطهم:
- العملاء النشطين (لديهم عمليات حديثة)
- العملاء غير النشطين (لا توجد عمليات حديثة)
- جميع العملاء

### عرض تفاصيل العميل

جدول العملاء يعرض المعلومات التالية لكل عميل:

- **الاسم:** الاسم الكامل للعميل
- **رقم الهوية:** رقم الهوية الوطنية أو الإقامة
- **الهاتف:** رقم الهاتف المسجل
- **العنوان:** العنوان المسجل
- **عدد العمليات:** إجمالي عدد العمليات التي تم تنفيذها للعميل
- **إجمالي المبلغ:** إجمالي قيمة جميع العمليات بالريال السعودي
- **تاريخ التسجيل:** تاريخ إضافة العميل إلى النظام
- **الإجراءات:** أزرار التعديل والحذف

### حذف العملاء

يمكن حذف العملاء غير النشطين من خلال:

1. **تحديد العميل:** ابحث عن العميل المراد حذفه
2. **النقر على زر الحذف:** انقر على زر "حذف" الأحمر
3. **تأكيد الحذف:** أكد عملية الحذف في النافذة المنبثقة

> **تحذير:** حذف العميل سيؤدي إلى حذف جميع البيانات المرتبطة به نهائياً. تأكد من عدم وجود عمليات أو فواتير معلقة قبل الحذف.

### إحصائيات العملاء

يعرض النظام إحصائيات مفيدة عن العملاء تشمل:

- **إجمالي عدد العملاء المسجلين**
- **عدد العملاء النشطين في الشهر الحالي**
- **متوسط قيمة العمليات لكل عميل**
- **أكثر العملاء نشاطاً**

---

## إدارة الموظفين

قسم إدارة الموظفين يوفر نظاماً شاملاً لإدارة الموارد البشرية في المعمل. يتضمن هذا القسم إدارة بيانات الموظفين وتخصصاتهم وتتبع أدائهم في العمليات المختلفة.

### إضافة موظف جديد

عملية إضافة موظف جديد تتطلب إدخال البيانات التالية:

**البيانات الشخصية:**
- **اسم الموظف (مطلوب):** الاسم الكامل كما هو مسجل في الهوية
- **رقم الهوية (مطلوب):** رقم الهوية الوطنية أو الإقامة
- **رقم الهاتف:** رقم الهاتف للتواصل

**البيانات الوظيفية:**
- **الوظيفة (مطلوب):** اختر من القائمة المنسدلة:
  - محلل: متخصص في تحليل العينات وتحديد نسب النقاء
  - فني تنقية: متخصص في عمليات تنقية المعادن
  - فني صب: متخصص في صب السبائك والقوالب
  - إداري: يتولى المهام الإدارية والمكتبية
  - مدير: يشرف على العمليات ويتخذ القرارات

### تقييم الأداء

النظام يتضمن نظام تقييم أداء متقدم يتتبع:

**مؤشرات الأداء الكمية:**
- عدد العمليات المنجزة
- معدل الإنجاز اليومي
- عدد ساعات العمل
- معدل الأخطاء

**مؤشرات الأداء النوعية:**
- جودة العمل المنجز
- الالتزام بالمواعيد
- التعاون مع الفريق
- الابتكار والتطوير

**نظام التقييم:**
يستخدم النظام مقياس من 1 إلى 5 نجوم:
- ⭐⭐⭐⭐⭐ (4.5-5.0): أداء ممتاز
- ⭐⭐⭐⭐ (4.0-4.4): أداء جيد جداً
- ⭐⭐⭐ (3.5-3.9): أداء جيد
- ⭐⭐ (3.0-3.4): أداء مقبول
- ⭐ (أقل من 3.0): أداء يحتاج تحسين

### إحصائيات الموظفين

يعرض النظام إحصائيات شاملة عن الموظفين:

**التوزيع حسب التخصص:**
- عدد المحللين
- عدد فنيي التنقية
- عدد فنيي الصب
- عدد الموظفين الإداريين

**إحصائيات الأداء:**
- متوسط تقييم الأداء العام
- أفضل موظف في الشهر
- معدل الإنتاجية لكل تخصص
- مؤشرات الحضور والانصراف

### إدارة المهام والمسؤوليات

لكل وظيفة مهام ومسؤوليات محددة:

**المحلل:**
- تحليل العينات المختلفة
- تحديد نسب النقاء والتركيب
- كتابة التقارير التقنية
- صيانة أجهزة التحليل

**فني التنقية:**
- تنقية المعادن من الشوائب
- تشغيل أفران التنقية
- مراقبة درجات الحرارة
- التعامل مع المواد الكيميائية

**فني الصب:**
- صب السبائك في القوالب
- تشغيل أفران الصب
- فحص جودة السبائك
- صيانة معدات الصب

**الإداري:**
- إدارة الملفات والوثائق
- التواصل مع العملاء
- إعداد الفواتير
- متابعة المواعيد

**المدير:**
- الإشراف على جميع العمليات
- اتخاذ القرارات الإستراتيجية
- إدارة الموارد البشرية
- ضمان الجودة والأمان

---

## إدارة العمليات

قسم إدارة العمليات هو القلب التقني للنظام ويغطي جميع العمليات الفنية في معمل الذهب. يتضمن ثلاثة أقسام رئيسية: تحليل العينات، التنقية، والصب.

### تحليل العينات

تحليل العينات هو الخطوة الأولى في معالجة المواد الخام ويتطلب دقة عالية ومعدات متخصصة.

**إضافة عملية تحليل جديدة:**

1. **اختيار العميل:** حدد العميل من القائمة المنسدلة
2. **إدخال الوزن:** أدخل وزن العينة بالغرام (يدعم الكسور العشرية)
3. **تحديد نوع المادة:** اختر من الأنواع التالية:
   - **ذهب خام:** المادة الخام المستخرجة من المناجم
   - **سبيكة:** سبائك ذهب مصنعة مسبقاً
   - **مجوهرات:** قطع مجوهرات مستعملة أو مكسورة
   - **أخرى:** أي مواد أخرى تحتوي على معادن نفيسة

4. **تعيين المحلل:** اختر المحلل المسؤول عن العملية
5. **حفظ العملية:** النظام سيحسب تكلفة التحليل تلقائياً

**حساب تكلفة التحليل:**
يحسب النظام التكلفة بناءً على:
- نوع المادة (لكل نوع سعر أساسي مختلف)
- وزن العينة (رسوم إضافية للعينات الثقيلة)
- تعقيد التحليل المطلوب

**نتائج التحليل:**
بعد إكمال التحليل، يتم تسجيل:
- نسبة النقاء بالنسبة المئوية
- وزن المعدن النقي
- نوع وكمية الشوائب
- تقرير تفصيلي عن التركيب

### عمليات التنقية

التنقية هي عملية إزالة الشوائب من المعادن للحصول على نقاء عالي.

**إضافة عملية تنقية:**

1. **اختيار العينة:** حدد العينة المراد تنقيتها من العينات المحللة
2. **الوزن الخام:** أدخل وزن المادة قبل التنقية
3. **المواد الكيميائية:** سجل المواد المستخدمة في التنقية
4. **تعيين الفني:** اختر فني التنقية المسؤول

**مراحل التنقية:**

**التحضير:**
- فحص المادة الخام
- تحديد طريقة التنقية المناسبة
- تحضير المواد الكيميائية
- إعداد المعدات

**التنقية الفعلية:**
- صهر المادة في درجة حرارة مناسبة
- إضافة المواد الكيميائية بالتسلسل الصحيح
- مراقبة التفاعلات الكيميائية
- فصل المعدن النقي عن الشوائب

**النتائج:**
- الوزن بعد التنقية
- نسبة الاسترداد (النسبة المئوية للمعدن المستخرج)
- وزن الفاقد
- نسبة النقاء النهائية

### عمليات الصب

الصب هو المرحلة الأخيرة حيث يتم تشكيل المعدن النقي في شكل سبائك.

**إضافة عملية صب:**

1. **اختيار العميل:** حدد العميل المطلوب صب السبيكة له
2. **وزن السبيكة:** أدخل الوزن المطلوب بالغرام
3. **شكل السبيكة:** اختر الشكل المطلوب:
   - **مستطيل:** الشكل التقليدي للسبائك
   - **دائري:** شكل دائري للاستخدامات الخاصة
   - **مربع:** شكل مربع للسبائك الصغيرة
   - **بيضاوي:** شكل بيضاوي للتصاميم المميزة

4. **نسبة النقاء:** أدخل نسبة النقاء المطلوبة
5. **تعيين الفني:** اختر فني الصب المسؤول

**عملية الصب:**

**التحضير:**
- تحضير القوالب المناسبة
- تسخين الفرن إلى درجة الحرارة المطلوبة
- فحص المعدن المراد صبه
- تحضير مواد التشطيب

**الصب الفعلي:**
- صهر المعدن في الفرن
- صب المعدن المنصهر في القوالب
- التبريد التدريجي
- إخراج السبيكة من القالب

**التشطيب:**
- تنظيف السطح
- تلميع السبيكة
- طباعة المعلومات (الوزن، النقاء، الرقم التسلسلي)
- الفحص النهائي للجودة

**الرقم التسلسلي:**
كل سبيكة تحصل على رقم تسلسلي فريد بالصيغة: GL-YYYY-XXXXXX
- GL: اختصار Gold Lab
- YYYY: السنة الحالية
- XXXXXX: رقم متسلسل من 6 أرقام

### تتبع العمليات

النظام يوفر تتبعاً شاملاً لجميع العمليات:

**حالات العمليات:**
- **قيد التحليل/المعالجة:** العملية بدأت ولم تكتمل بعد
- **مكتمل:** العملية انتهت بنجاح
- **معلق:** العملية متوقفة لسبب ما
- **ملغى:** العملية تم إلغاؤها

**التقارير التقنية:**
لكل عملية يتم إنشاء تقرير تقني يتضمن:
- تفاصيل المواد المستخدمة
- الظروف التقنية (درجة الحرارة، الوقت، إلخ)
- النتائج المحققة
- ملاحظات الفني المسؤول
- صور للمنتج النهائي (إن أمكن)


---

## إدارة الفواتير

نظام إدارة الفواتير يوفر حلاً شاملاً لإدارة الجوانب المالية للمعمل. يتضمن إنشاء الفواتير وتتبع المدفوعات وإدارة المبالغ المستحقة.

### إنشاء فاتورة جديدة

عملية إنشاء فاتورة جديدة تتطلب المعلومات التالية:

**البيانات الأساسية:**
- **العميل (مطلوب):** اختر العميل من القائمة المنسدلة
- **المبلغ الإجمالي (مطلوب):** أدخل المبلغ الإجمالي بالريال السعودي
- **وصف الخدمة (مطلوب):** وصف تفصيلي للخدمات المقدمة
- **المبلغ المدفوع مقدماً:** أي مبلغ تم دفعه مسبقاً (اختياري)

**حساب المبالغ:**
النظام يحسب تلقائياً:
- **المبلغ الإجمالي:** إجمالي قيمة الخدمات
- **المبلغ المدفوع:** ما تم دفعه فعلياً
- **المبلغ المستحق:** الفرق بين الإجمالي والمدفوع

**رقم الفاتورة:**
كل فاتورة تحصل على رقم فريد بالصيغة: INV-YYYY-XXXXXX
- INV: اختصار Invoice
- YYYY: السنة الحالية
- XXXXXX: رقم متسلسل من 6 أرقام

### حالات الفواتير

النظام يدعم أربع حالات للفواتير:

**مدفوع:**
- تم دفع المبلغ الإجمالي بالكامل
- لا توجد مبالغ مستحقة
- الفاتورة مغلقة ولا تحتاج متابعة

**غير مدفوع:**
- لم يتم دفع أي مبلغ من الفاتورة
- المبلغ المستحق يساوي المبلغ الإجمالي
- تحتاج متابعة للتحصيل

**مدفوع جزئياً:**
- تم دفع جزء من المبلغ الإجمالي
- يوجد مبلغ مستحق للدفع
- يمكن إضافة دفعات إضافية

**ملغى:**
- تم إلغاء الفاتورة لأي سبب
- لا يمكن تعديلها أو إضافة دفعات لها
- تظهر في التقارير للمراجعة فقط

### إدارة المدفوعات

النظام يدعم إدارة متقدمة للمدفوعات:

**إضافة دفعة جديدة:**
1. **اختيار الفاتورة:** ابحث عن الفاتورة المطلوبة
2. **النقر على زر الدفع:** انقر على زر "دفع" بجانب الفاتورة
3. **إدخال مبلغ الدفعة:** أدخل المبلغ المراد دفعه
4. **تأكيد الدفعة:** انقر على "إضافة الدفعة"

**قيود الدفع:**
- لا يمكن دفع مبلغ أكبر من المبلغ المستحق
- لا يمكن إضافة دفعات للفواتير الملغاة
- جميع المبالغ يجب أن تكون أرقام موجبة

**تاريخ الدفعات:**
النظام يحتفظ بسجل كامل لجميع الدفعات:
- تاريخ ووقت كل دفعة
- مبلغ الدفعة
- المستخدم الذي سجل الدفعة
- طريقة الدفع (نقد، تحويل، شيك)

### البحث والفلترة

النظام يوفر إمكانيات بحث وفلترة متقدمة:

**البحث النصي:**
يمكن البحث باستخدام:
- اسم العميل
- رقم الفاتورة
- وصف الخدمة

**الفلترة حسب الحالة:**
- جميع الفواتير
- الفواتير المدفوعة
- الفواتير غير المدفوعة
- الفواتير المدفوعة جزئياً
- الفواتير الملغاة

**الفلترة حسب التاريخ:**
- فواتير اليوم
- فواتير الأسبوع الحالي
- فواتير الشهر الحالي
- نطاق تاريخ مخصص

### الإحصائيات المالية

يعرض النظام إحصائيات مالية شاملة:

**الإحصائيات العامة:**
- إجمالي عدد الفواتير
- إجمالي المبالغ المفوترة
- إجمالي المبالغ المحصلة
- إجمالي المبالغ المستحقة

**مؤشرات الأداء:**
- معدل التحصيل (نسبة المبالغ المحصلة إلى المفوترة)
- متوسط قيمة الفاتورة
- متوسط وقت التحصيل
- عدد الفواتير المتأخرة

### التقارير المالية

النظام ينشئ تقارير مالية مفصلة:

**تقرير الفواتير اليومي:**
- جميع الفواتير المنشأة في يوم محدد
- إجمالي المبالغ المفوترة
- إجمالي المبالغ المحصلة

**تقرير المبالغ المستحقة:**
- جميع الفواتير غير المدفوعة
- ترتيب حسب تاريخ الاستحقاق
- تصنيف حسب مدة التأخير

**تقرير أداء التحصيل:**
- معدلات التحصيل الشهرية
- مقارنة بالأشهر السابقة
- توقعات التحصيل المستقبلي

---

## التقارير والإحصائيات

قسم التقارير يوفر رؤى شاملة وتحليلات متقدمة لجميع جوانب عمل المعمل. يتضمن أربعة أنواع من التقارير: اليومية والأسبوعية والشهرية والمخصصة.

### أنواع التقارير

**التقرير اليومي:**
يغطي أنشطة يوم واحد محدد ويتضمن:
- إحصائيات العمليات اليومية
- الأداء المالي لليوم
- أداء الموظفين
- أهم العملاء لليوم

**التقرير الأسبوعي:**
يلخص أنشطة أسبوع كامل ويوفر:
- مقارنة الأداء بين أيام الأسبوع
- اتجاهات الأداء الأسبوعية
- تحليل الذروة والانخفاض
- توصيات للأسبوع القادم

**التقرير الشهري:**
يقدم نظرة شاملة على الشهر ويشمل:
- إحصائيات شاملة للشهر
- مقارنة مع الأشهر السابقة
- تحليل الاتجاهات الشهرية
- تقييم تحقيق الأهداف

**التقرير المخصص:**
يسمح بتحديد نطاق تاريخ مخصص ويوفر:
- مرونة في اختيار الفترة الزمنية
- تحليل فترات محددة
- مقارنات مخصصة
- تقارير للمناسبات الخاصة

### محتويات التقارير

**إحصائيات العمليات:**
- عدد العينات المحللة
- عدد عمليات التنقية المنجزة
- عدد السبائك المصبوبة
- إجمالي وزن السبائك المنتجة

**الإحصائيات المالية:**
- إجمالي عدد الفواتير
- إجمالي المبالغ المفوترة
- إجمالي المبالغ المحصلة
- المبالغ المستحقة
- معدل التحصيل

**أداء الموظفين:**
- عدد العمليات لكل موظف
- تقييم الأداء
- معدل الإنتاجية
- مؤشرات الجودة

**تحليل العملاء:**
- أكثر العملاء نشاطاً
- إجمالي الإيرادات من كل عميل
- تحليل أنماط التعامل
- العملاء الجدد والمفقودين

### إعدادات التقارير

**اختيار الفترة الزمنية:**
- للتقرير اليومي: اختيار تاريخ محدد
- للتقرير الأسبوعي: اختيار بداية الأسبوع
- للتقرير الشهري: اختيار الشهر والسنة
- للتقرير المخصص: تحديد تاريخ البداية والنهاية

**خيارات العرض:**
- عرض مفصل أو ملخص
- تضمين الرسوم البيانية
- اختيار العملة (ريال سعودي افتراضياً)
- تحديد مستوى التفاصيل

### تصدير التقارير

النظام يدعم تصدير التقارير بصيغ متعددة:

**تصدير JSON:**
- صيغة مناسبة للمعالجة البرمجية
- يحتفظ بجميع البيانات والهيكل
- سهل الاستيراد في أنظمة أخرى
- مناسب للنسخ الاحتياطي

**تصدير PDF:**
- صيغة مناسبة للطباعة والأرشفة
- تصميم احترافي ومنسق
- يتضمن الشعار والمعلومات الأساسية
- مناسب للعرض على العملاء

**تصدير Excel (مستقبلي):**
- صيغة مناسبة للتحليل المتقدم
- يدعم الجداول والرسوم البيانية
- سهل التعديل والمعالجة
- مناسب للتحليل المالي

### الرسوم البيانية والمؤشرات

التقارير تتضمن مؤشرات بصرية متقدمة:

**مؤشرات الأداء الرئيسية (KPIs):**
- معدل نمو الإيرادات
- معدل رضا العملاء
- كفاءة العمليات
- معدل استخدام الطاقة الإنتاجية

**الرسوم البيانية:**
- رسوم بيانية خطية للاتجاهات
- رسوم دائرية للتوزيعات
- رسوم عمودية للمقارنات
- خرائط حرارية للأنشطة

**المقارنات الزمنية:**
- مقارنة مع الفترة السابقة
- مقارنة مع نفس الفترة من العام الماضي
- اتجاهات النمو أو الانخفاض
- توقعات الفترات القادمة

---

## الإعدادات والصيانة

### إعدادات النظام

**إعدادات العرض:**
- اللغة: العربية (افتراضي) أو الإنجليزية
- المنطقة الزمنية: توقيت المملكة العربية السعودية
- تنسيق التاريخ: هجري أو ميلادي
- العملة: الريال السعودي (افتراضي)

**إعدادات الأمان:**
- مدة انتهاء الجلسة: 30 دقيقة (افتراضي)
- تعقيد كلمة المرور: متوسط (افتراضي)
- تسجيل العمليات: مفعل (افتراضي)
- النسخ الاحتياطي التلقائي: يومي (افتراضي)

### النسخ الاحتياطي

**النسخ الاحتياطي التلقائي:**
- يتم إنشاء نسخة احتياطية يومياً في الساعة 2:00 صباحاً
- يتم الاحتفاظ بـ 30 نسخة احتياطية
- النسخ الأقدم يتم حذفها تلقائياً
- يتم ضغط النسخ لتوفير المساحة

**النسخ الاحتياطي اليدوي:**
- يمكن إنشاء نسخة احتياطية في أي وقت
- يتم حفظ النسخة في مجلد منفصل
- يمكن تصدير النسخة إلى جهاز خارجي
- يتضمن جميع البيانات والإعدادات

### الصيانة الدورية

**تنظيف قاعدة البيانات:**
- حذف السجلات المؤقتة القديمة
- ضغط قاعدة البيانات لتحسين الأداء
- إعادة فهرسة الجداول
- تحسين استعلامات البحث

**تحديث النظام:**
- فحص التحديثات المتاحة
- تحميل وتثبيت التحديثات
- اختبار النظام بعد التحديث
- استعادة النسخة السابقة عند الحاجة

---

## استكشاف الأخطاء وإصلاحها

### المشاكل الشائعة وحلولها

**مشكلة: بطء في تحميل الصفحات**
الأسباب المحتملة:
- اتصال إنترنت ضعيف
- امتلاء ذاكرة التخزين المؤقت
- كثرة البيانات في قاعدة البيانات

الحلول:
- تحقق من سرعة الإنترنت
- امسح ذاكرة التخزين المؤقت للمتصفح
- قم بتنظيف قاعدة البيانات
- أعد تشغيل النظام

**مشكلة: خطأ في تسجيل الدخول**
الأسباب المحتملة:
- كلمة مرور خاطئة
- انتهاء صلاحية الحساب
- مشكلة في قاعدة البيانات

الحلول:
- تأكد من صحة اسم المستخدم وكلمة المرور
- تواصل مع مدير النظام لإعادة تعيين كلمة المرور
- تحقق من حالة قاعدة البيانات

**مشكلة: فقدان البيانات**
الأسباب المحتملة:
- انقطاع الكهرباء المفاجئ
- عطل في القرص الصلب
- خطأ في النظام

الحلول:
- استعادة آخر نسخة احتياطية
- تحقق من سلامة القرص الصلب
- تواصل مع الدعم التقني

### رسائل الخطأ الشائعة

**"خطأ في الاتصال بقاعدة البيانات"**
- تحقق من وجود ملف قاعدة البيانات
- تأكد من صلاحيات الوصول للملف
- أعد تشغيل النظام

**"انتهت صلاحية الجلسة"**
- سجل دخول مرة أخرى
- تحقق من إعدادات الجلسة
- امسح ملفات تعريف الارتباط

**"خطأ في حفظ البيانات"**
- تحقق من مساحة القرص الصلب
- تأكد من صحة البيانات المدخلة
- أعد المحاولة بعد قليل

### الدعم التقني

**معلومات الاتصال:**
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-11-1234567
- ساعات العمل: من الأحد إلى الخميس، 8:00 ص - 5:00 م

**المعلومات المطلوبة عند طلب الدعم:**
- وصف مفصل للمشكلة
- رسائل الخطأ (إن وجدت)
- خطوات إعادة إنتاج المشكلة
- إصدار النظام المستخدم
- نوع المتصفح والإصدار

---

## الخلاصة

نظام إدارة معمل الذهب هو حل شامل ومتكامل يوفر جميع الأدوات اللازمة لإدارة معمل الذهب بكفاءة عالية. من خلال واجهته العربية السهلة والميزات المتقدمة، يساعد النظام في تحسين الإنتاجية وضمان الجودة وتحقيق أفضل النتائج المالية.

النظام قابل للتطوير والتخصيص حسب احتياجات كل معمل، ويتضمن نظام دعم فني شامل لضمان التشغيل السلس والمستمر.

---

**تم إعداد هذا الدليل بواسطة:** Manus AI  
**تاريخ الإصدار:** 1 يوليو 2025  
**إصدار النظام:** 1.0.0  
**حقوق الطبع والنشر:** © 2025 نظام إدارة معمل الذهب

