#!/usr/bin/env python3
"""
تشغيل مباشر لتطبيق سطح المكتب
Direct Desktop Application Launcher
"""

if __name__ == "__main__":
    try:
        from desktop_app import GoldLabDesktopApp
        
        print("🚀 تشغيل نظام إدارة معمل الذهب...")
        print("🖥️  فتح تطبيق سطح المكتب...")
        
        app = GoldLabDesktopApp()
        app.run()
        
        print("✅ تم إغلاق التطبيق بنجاح")
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        print("تأكد من وجود جميع الملفات المطلوبة")
        input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")
