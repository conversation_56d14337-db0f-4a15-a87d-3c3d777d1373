"""
مدير قاعدة البيانات لبرنامج إدارة معمل الذهب
يتضمن إنشاء قاعدة البيانات والجداول وعمليات CRUD الأساسية
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple


class DatabaseManager:
    """مدير قاعدة البيانات الرئيسي"""
    
    def __init__(self, db_path: str = "gold_lab.db"):
        """
        تهيئة مدير قاعدة البيانات
        
        Args:
            db_path: مسار ملف قاعدة البيانات
        """
        self.db_path = db_path
        self.connection = None
        self.create_database()
    
    def connect(self) -> sqlite3.Connection:
        """إنشاء اتصال بقاعدة البيانات"""
        if self.connection is None:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return self.connection
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def create_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = self.connect()
        cursor = conn.cursor()
        
        # جدول العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Customers (
                customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                id_number TEXT UNIQUE NOT NULL,
                phone TEXT,
                address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الموظفين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Employees (
                employee_id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                id_number TEXT UNIQUE NOT NULL,
                role TEXT NOT NULL,
                phone TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول العينات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Samples (
                sample_id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                weight REAL NOT NULL,
                material_type TEXT NOT NULL,
                purity_percentage REAL,
                impurities TEXT,
                analysis_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                analysis_cost REAL,
                employee_id INTEGER,
                FOREIGN KEY (customer_id) REFERENCES Customers(customer_id),
                FOREIGN KEY (employee_id) REFERENCES Employees(employee_id)
            )
        ''')
        
        # جدول التنقية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Refinements (
                refinement_id INTEGER PRIMARY KEY AUTOINCREMENT,
                sample_id INTEGER NOT NULL,
                raw_weight REAL NOT NULL,
                refined_weight REAL,
                loss_weight REAL,
                chemicals_used TEXT,
                refinement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                recovery_percentage REAL,
                employee_id INTEGER,
                FOREIGN KEY (sample_id) REFERENCES Samples(sample_id),
                FOREIGN KEY (employee_id) REFERENCES Employees(employee_id)
            )
        ''')
        
        # جدول الصب
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Castings (
                casting_id INTEGER PRIMARY KEY AUTOINCREMENT,
                refinement_id INTEGER,
                customer_id INTEGER NOT NULL,
                bar_weight REAL NOT NULL,
                bar_shape TEXT NOT NULL,
                purity_percentage REAL NOT NULL,
                serial_number TEXT UNIQUE NOT NULL,
                casting_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                employee_id INTEGER,
                FOREIGN KEY (refinement_id) REFERENCES Refinements(refinement_id),
                FOREIGN KEY (customer_id) REFERENCES Customers(customer_id),
                FOREIGN KEY (employee_id) REFERENCES Employees(employee_id)
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Invoices (
                invoice_id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                sample_id INTEGER,
                refinement_id INTEGER,
                casting_id INTEGER,
                total_amount REAL NOT NULL,
                paid_amount REAL DEFAULT 0,
                status TEXT NOT NULL,
                invoice_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES Customers(customer_id),
                FOREIGN KEY (sample_id) REFERENCES Samples(sample_id),
                FOREIGN KEY (refinement_id) REFERENCES Refinements(refinement_id),
                FOREIGN KEY (casting_id) REFERENCES Castings(casting_id)
            )
        ''')
        
        # جدول التقارير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Reports (
                report_id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_type TEXT NOT NULL,
                start_date TIMESTAMP NOT NULL,
                end_date TIMESTAMP NOT NULL,
                total_samples INTEGER,
                total_casted_weight REAL,
                total_revenue REAL,
                generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المستخدمين (للأمان)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Users (
                user_id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
    
    def execute_query(self, query: str, params: tuple = ()) -> List[sqlite3.Row]:
        """
        تنفيذ استعلام SELECT وإرجاع النتائج
        
        Args:
            query: استعلام SQL
            params: معاملات الاستعلام
            
        Returns:
            قائمة بالنتائج
        """
        conn = self.connect()
        cursor = conn.cursor()
        cursor.execute(query, params)
        return cursor.fetchall()
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """
        تنفيذ استعلام INSERT/UPDATE/DELETE
        
        Args:
            query: استعلام SQL
            params: معاملات الاستعلام
            
        Returns:
            معرف السجل المدرج (للـ INSERT) أو عدد الصفوف المتأثرة
        """
        conn = self.connect()
        cursor = conn.cursor()
        cursor.execute(query, params)
        conn.commit()
        return cursor.lastrowid if cursor.lastrowid else cursor.rowcount
    
    def get_table_data(self, table_name: str, limit: int = None) -> List[Dict]:
        """
        الحصول على جميع البيانات من جدول معين
        
        Args:
            table_name: اسم الجدول
            limit: حد أقصى لعدد السجلات
            
        Returns:
            قائمة بالسجلات كقواميس
        """
        query = f"SELECT * FROM {table_name}"
        if limit:
            query += f" LIMIT {limit}"
        
        rows = self.execute_query(query)
        return [dict(row) for row in rows]
    
    def backup_database(self, backup_path: str) -> bool:
        """
        إنشاء نسخة احتياطية من قاعدة البيانات
        
        Args:
            backup_path: مسار ملف النسخة الاحتياطية
            
        Returns:
            True إذا تم إنشاء النسخة الاحتياطية بنجاح
        """
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            return True
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def __del__(self):
        """تنظيف الموارد عند حذف الكائن"""
        self.disconnect()


# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء مدير قاعدة البيانات
    db_manager = DatabaseManager("test_gold_lab.db")
    
    # اختبار إنشاء قاعدة البيانات
    print("تم إنشاء قاعدة البيانات بنجاح!")
    
    # عرض الجداول المتاحة
    tables = db_manager.execute_query(
        "SELECT name FROM sqlite_master WHERE type='table'"
    )
    print("الجداول المتاحة:")
    for table in tables:
        print(f"- {table['name']}")
    
    # تنظيف
    db_manager.disconnect()

